import 'package:flnewpay_core/api/api_config.dart';
import 'package:flnewpay_core/api/http.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';
import 'package:flnewpay_core/common/app_config.dart';
import 'package:flnewpay_core/common/channels/event_manager.dart';
import 'package:flnewpay_core/pages/merchantApp/MerchantApp.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantenviromentchange/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:flnewpay_core/common/utils/log.dart';
import 'package:flnewpay_core/common/services/language_service.dart';
// ignore: depend_on_referenced_packages
import 'package:get/get.dart';

// LanguageService _languageService = LanguageService();

void main() async {
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    Log.v('Flutter错误: ${details.exception}');
    Log.v('堆栈跟踪: ${details.stack}');
  };
  // 捕获异步错误
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();
      EventManager();
      await MerchantUser.initSPUser();
      // 锁定屏幕方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
      // 捕获 Flutter 框架错误
      Log.setLogLevel(LogLevel.verbose);
      Log.setEnabled(true);
      Log.setSaveToFile(true);
      Log.setTag('MerchantApp');
      // 初始化应用配置
      final appConfig = AppConfig();
      appConfig.init();
      appConfig.setAppType(AppType.merchant);
      // 从本地存储加载环境设置
      final savedEnvironment =
          await MerchantEnvironmentChangeController.loadEnvironmentFromLocal(
            defaultEnvironment: ApiEnvironment.dev,
          );
      // final savedEnvironment =
      //     await MerchantEnvironmentChangeController.loadEnvironmentFromLocal(
      //   defaultEnvironment: ApiEnvironment.prod,
      // );
      ApiConfig().setEnvironment(savedEnvironment);

      // 更新HTTP实例的baseUrl
      final baseUrl = ApiConfig().baseUrl;
      Http().setBaseURL(baseUrl);

      Log.v('应用启动，加载环境: $savedEnvironment');
      Log.v('应用启动，baseUrl: $baseUrl');
      await Get.putAsync<LanguageService>(() async {
        final service = LanguageService();
        await service.init();
        return service;
      }, permanent: true);
      Log.v('应用初始化完成，准备运行MerchantApp');
      runApp(const MerchantApp());
    },
    (error, stackTrace) {
      Log.v('未捕获的异步错误: $error');
      Log.v('堆栈跟踪: $stackTrace');
    },
  );
}
