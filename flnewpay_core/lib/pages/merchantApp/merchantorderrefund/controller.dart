import 'package:flnewpay_core/api/models/newpaydefine.dart';
import 'package:flnewpay_core/common/extensions/date_time_ex.dart';
import 'package:flnewpay_core/common/routes/app_pages.dart';
import 'package:flnewpay_core/common/utils/flnewpayalter.dart';
import 'package:flnewpay_core/common/utils/flnewpaypop_util.dart';
import 'package:flnewpay_core/common/utils/log.dart';
import 'package:flnewpay_core/common/utils/route_utils.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrefund/orderrecordresult.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';
import 'package:flnewpay_core/api/merchant_api.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MerchantOrderRefundController extends GetxController {
  final refreshController = RefreshController();
  final selectMerhcantuser = MerchantUser.instance.localBusiName.obs;
  final page = 1.obs;
  final pageSize = 10.obs;
  final selectType = OrderRefundSelectType.ORDER_MANAGEMENT_LIST.obs;
  final payMethod = NewPayMethod.pay_method_all.obs;
  final orderRecords = <OrderRefundRecord>[].obs;
  // 日期范围
  final startDate = DateTimeExtension.getDefaultStartData().obs;
  final endDate = DateTime.now().obs;
  @override
  void onInit() {
    super.onInit();
    selectType.value = Get.arguments?['selectType'] as OrderRefundSelectType;
    loadData();
  }

  void onPaymethodChanged(String type) {
    payMethod.value = NewPayMethod.values.firstWhere(
      (e) => e.type == type,
      orElse: () => NewPayMethod.pay_method_all,
    );
    loadData();
  }

  void onRefresh() {
    loadData();
  }

  Future<void> loadData() async {
    orderRecords.value = [];
    refreshController.resetNoData();
    page.value = 1;
    final response = await MerchantApi().shopBillListQuery(
      page.value,
      pageSize.value,
      MerchantUser.instance.shopId ?? '',
      payMethod.value,
      startDate.value.toTimestamp(),
      endDate.value.toTimestamp(),
      selectType.value,
    );
    refreshController.refreshCompleted();
    final result = response.$1 ?? [];
    final total = response.$2 ?? 0;
    if (result.isNotEmpty) {
      final newdatas = result;
      if (newdatas.isNotEmpty) {
        orderRecords.value = newdatas;
        if (orderRecords.length >= total) {
          refreshController.loadNoData();
        } else {
          refreshController.refreshCompleted();
          orderRecords.refresh();
        }
      } else {
        refreshController.loadNoData();
      }
    }
    update(['orderRecords']);
  }

  Future<void> loadMore() async {
    int tempValue = page.value + 1;
    final result = await MerchantApi().shopBillListQuery(
      tempValue,
      pageSize.value,
      MerchantUser.instance.shopId ?? '',
      payMethod.value,
      startDate.value.toTimestamp(),
      endDate.value.toTimestamp(),
      selectType.value,
    );
    refreshController.loadComplete();
    final newdatas = result.$1 ?? [];
    final total = result.$2 ?? 0;
    if (newdatas.isNotEmpty) {
      page.value = tempValue;
      orderRecords.value = [...orderRecords, ...newdatas];
      if (orderRecords.length >= total) {
        refreshController.loadNoData();
      } else {
        orderRecords.refresh();
        refreshController.loadComplete();
      }
    } else {
      page.value = tempValue - 1;
      refreshController.loadComplete();
    }
    update(['orderRecords']);
  }

  // 选择开始日期
  Future<void> selectStartDate(BuildContext context) async {
    final now = DateTime.now();
    final oneYear = now.subtract(const Duration(days: 365 * 1));

    // 确保初始日期在合理范围内
    DateTime initialStartDate = startDate.value;
    DateTime initialEndDate = endDate.value;

    // 如果初始日期不在合理范围内，重置为默认值
    if (initialStartDate.isAfter(now) || initialStartDate.isBefore(oneYear)) {
      initialStartDate = now.subtract(const Duration(days: 6));
      initialEndDate = now;
    }

    final result = await FlNewPayPopUtil.showCustomDateRangePicker(
      initialStartDate: initialStartDate,
      initialEndDate: initialEndDate,
      firstDate: oneYear,
      lastDate: now,
      isSelectingStart: true,
      maxHistoryDays: 365,
      onDateTimeChanged: (start, end) {
        // 实时更新日期范围
        startDate.value = start;
        endDate.value = end;
        startDate.refresh();
        endDate.refresh();
      },
    );

    if (result != null) {
      // 最终确认时再次验证日期范围
      DateTime newStartDate = result.start;
      DateTime newEndDate = result.end;
      startDate.value = newStartDate;
      endDate.value = newEndDate;
      startDate.refresh();
      endDate.refresh();
      loadData();
    }
  }

  // 选择结束日期
  Future<void> selectEndDate(BuildContext context) async {
    final now = DateTime.now();
    final oneYear = now.subtract(const Duration(days: 365 * 1));

    // 确保初始日期在合理范围内
    DateTime initialStartDate = startDate.value;
    DateTime initialEndDate = endDate.value;

    // 如果初始日期不在合理范围内，重置为默认值
    if (initialEndDate.isAfter(now)) {
      initialEndDate = now;
      initialStartDate = now.subtract(const Duration(days: 6));
    }

    final result = await FlNewPayPopUtil.showCustomDateRangePicker(
      initialStartDate: initialStartDate,
      initialEndDate: initialEndDate,
      firstDate: oneYear,
      lastDate: now,
      maxHistoryDays: 365,
      isSelectingStart: false,
      onDateTimeChanged: (start, end) {
        // 实时更新日期范围
        startDate.value = start;
        endDate.value = end;
        startDate.refresh();
        endDate.refresh();
      },
    );

    if (result != null) {
      // 最终确认时再次验证日期范围
      DateTime newStartDate = result.start;
      DateTime newEndDate = result.end;
      // 如果结束日期超过当前时间，调整两个日期
      if (newEndDate.isAfter(now)) {
        newEndDate = now;
        newStartDate = newEndDate.subtract(const Duration(days: 6));
      }

      startDate.value = newStartDate;
      endDate.value = newEndDate;
      loadData();
    }
  }

  Future<void> selectPayMethod(BuildContext context) async {
    final result = await NewPayAlert.showPaymethodList(
      paymethods: NewPayMethod.values,
    );
    if (result != null) {
      payMethod.value = NewPayMethod.values.firstWhere(
        (e) => e.displayName == result.displayName,
        orElse: () => NewPayMethod.pay_method_all,
      );
      payMethod.value = result;
      update(['pay_method_selector']);
      loadData();
    }
  }

  // 加载统计数据
  Future<void> loadAnalyticsData() async {
    try {
      // TODO: 调用API获取数据
      // 模拟数据
    } catch (e) {
      Log.v('加载数据失败: $e');
    }
  }

  VoidCallback onOrderRecordTap(int index) {
    return () => RouteUtils.toNamed(
      AppPages.merchantorderdetail,
      arguments: {'orderNo': orderRecords[index].orderNo},
    );
  }
}
