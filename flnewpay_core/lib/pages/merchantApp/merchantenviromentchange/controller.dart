import 'package:flnewpay_core/api/api_config.dart';
import 'package:flnewpay_core/api/http.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';
import 'package:flnewpay_core/common/utils/flnewpayalter.dart';
import 'package:flnewpay_core/common/utils/log.dart';
import 'package:flnewpay_core/common/routes/app_pages.dart';
import 'package:flnewpay_core/common/utils/route_utils.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';

class MerchantEnvironmentChangeController extends GetxController {
  // 当前选中的环境
  final currentEnvironment = ApiEnvironment.dev.obs;

  // 环境列表
  final List<EnvironmentItem> environments = [
    EnvironmentItem(
      environment: ApiEnvironment.dev,
      name: '开发环境',
      description: '用于开发和测试',
      baseUrl: 'http://**************:8084/',
    ),
    // EnvironmentItem(
    //   environment: ApiEnvironment.pre,
    //   name: '预发布环境',
    //   description: '用于预发布测试',
    //   baseUrl: 'https://pre.newpay.la:4040/',
    // ),
    EnvironmentItem(
      environment: ApiEnvironment.prod,
      name: '生产环境',
      description: '正式生产环境',
      baseUrl: 'https://prod.newpay.la:4040/',
    ),
  ];

  @override
  void onInit() {
    super.onInit();
    // 获取当前环境
    currentEnvironment.value = ApiConfig().currentEnvironment;
    Log.v('当前环境: ${currentEnvironment.value}');
  }

  // 选择环境
  void selectEnvironment(ApiEnvironment environment) {
    currentEnvironment.value = environment;
    Log.v('MerchantApp', '选择环境: $environment');
    update(['environment_list', 'confirm_button']);
  }

  // 获取环境项
  EnvironmentItem getEnvironmentItem(ApiEnvironment environment) {
    return environments.firstWhere((item) => item.environment == environment);
  }

  // 确认切换环境
  void confirmChangeEnvironment() {
    final selectedEnv = getEnvironmentItem(currentEnvironment.value);

    ConfirmAlterCenter.showConfirmDialog(
      title: 'confirm_environment_change'.tr,
      content:
          '${'switch_to'.tr} ${selectedEnv.name}\n${selectedEnv.baseUrl}\n\n${'app_will_restart'.tr}',
      onConfirm: () {
        _changeEnvironmentAndRestart();
      },
    );
  }

  // 切换环境并重启应用
  Future<void> _changeEnvironmentAndRestart() async {
    try {
      // 保存环境设置到本地存储
      await _saveEnvironmentToLocal(currentEnvironment.value);

      // 设置新环境
      ApiConfig().setEnvironment(currentEnvironment.value);

      // 更新HTTP实例的baseUrl
      final newBaseUrl = ApiConfig().baseUrl;
      Http().setBaseURL(newBaseUrl);
      Log.v('环境已切换至: ${currentEnvironment.value}');
      Log.v('新的baseUrl: $newBaseUrl');
      // 测试新环境
      // await testCurrentEnvironment();

      // 显示切换成功提示
      NewPayAlert.showSuccessWithMessage('environment_changed_successfully'.tr);

      // 延迟一下再重启应用
      await Future.delayed(const Duration(milliseconds: 1500));

      // 重启应用
      await _restartApp();
    } catch (e) {
      Log.v('环境切换失败: $e');
      NewPayAlert.showFailureWithMessage('environment_change_failed'.tr);
    }
  }

  // 保存环境设置到本地存储
  Future<void> _saveEnvironmentToLocal(ApiEnvironment environment) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('api_environment', environment.name);
    Log.v('环境设置已保存: ${environment.name}');
  }

  // 从本地存储加载环境设置
  static Future<ApiEnvironment> loadEnvironmentFromLocal({
    ApiEnvironment? defaultEnvironment,
  }) async {
    if (defaultEnvironment != null) {
      return defaultEnvironment;
    }
    try {
      final prefs = await SharedPreferences.getInstance();
      final envName = prefs.getString('api_environment');
      if (envName != null) {
        switch (envName) {
          case 'dev':
            return ApiEnvironment.dev;
          case 'pre':
            return ApiEnvironment.pre;
          case 'prod':
            return ApiEnvironment.prod;
          default:
            return ApiEnvironment.dev;
        }
      }
    } catch (e) {
      Log.v('加载环境设置失败: $e');
    }
    return ApiEnvironment.prod; // 默认开发环境
  }

  // 重启应用
  Future<void> _restartApp() async {
    try {
      // 清除所有路由栈，跳转到根路由
      await MerchantUser.instance.logout(showConfirm: false);
      await RouteUtils.offAllNamed(AppPages.merchantRoot);

      // 如果上面的方法不够彻底，可以使用系统级重启
      // 注意：这需要添加 restart_app 依赖
      // RestartWidget.restartApp(Get.context!);
    } catch (e) {
      Log.v('应用重启失败: $e');
      // 备用方案：退出应用
      SystemNavigator.pop();
    }
  }

  // 重置到默认环境
  void resetToDefaultEnvironment() {
    ConfirmAlterCenter.showConfirmDialog(
      title: 'reset_environment'.tr,
      content: 'reset_to_dev_environment'.tr,
      onConfirm: () async {
        currentEnvironment.value = ApiEnvironment.dev;
        await _changeEnvironmentAndRestart();
      },
    );
  }

  // 测试当前环境的API连接
  Future<void> testCurrentEnvironment() async {
    try {
      Log.v('MerchantApp', '测试当前环境API连接...');
      Log.v('MerchantApp', '当前ApiConfig baseUrl: ${ApiConfig().baseUrl}');
      Log.v('MerchantApp', '当前Http baseUrl: ${Http().dio.options.baseUrl}');

      // 这里可以添加一个简单的API测试请求
      // 比如调用一个健康检查接口
    } catch (e) {
      Log.v('MerchantApp', '环境测试失败: $e');
    }
  }
}

// 环境项数据模型
class EnvironmentItem {
  final ApiEnvironment environment;
  final String name;
  final String description;
  final String baseUrl;

  EnvironmentItem({
    required this.environment,
    required this.name,
    required this.description,
    required this.baseUrl,
  });
}
