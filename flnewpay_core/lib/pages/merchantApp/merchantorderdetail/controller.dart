import 'package:flnewpay_core/api/models/merchant/merchant_order_refund_create_result.dart';
import 'package:flnewpay_core/api/models/newpaydefine.dart';
import 'package:get/get.dart';
import 'package:flnewpay_core/common/utils/flnewpayalter.dart';
import 'package:flnewpay_core/api/merchant_api.dart';
import 'package:flnewpay_core/api/models/merchant/order_bill_detail_result.dart';
import 'package:decimal/decimal.dart';

class MerchantOrderDetailController extends GetxController {
  final orderNo = ''.obs;
  final orderBillDetailResult = OrderBillDetailResult().obs;
  final canBackMoney = false.obs;
  final canRefundMoney = Decimal.zero.obs;
  final isRefreshing = false.obs;
  final refundOrderNo = ''.obs;
  final currentRefundOrderAmt = '0'.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments?['orderNo'] != null) {
      orderNo.value = Get.arguments?['orderNo'].toString() ?? '';
      loadOrderDetail();
    }
  }

  Future<void> loadOrderDetail() async {
    isRefreshing.value = true;
    final result = await MerchantApi().shopBillDetailQuery(orderNo.value);
    isRefreshing.value = false;
    if (result.isSuccess) {
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        NewPayAlert.showFailureWithMessage(
          'un_support_order'.tr,
          onComplete: () => Get.back(),
        );
        return;
      }
      final detail = OrderBillDetailResult.fromJson(data ?? {});
      orderBillDetailResult.value = detail;
      Decimal allRefundOrderAmt =
          orderBillDetailResult.value.caculatorTotalRefundAmount();
      Decimal orderAmt = Decimal.parse(
        orderBillDetailResult.value.orderAmt?.toString() ?? '0',
      );
      if (allRefundOrderAmt < orderAmt) {
        canBackMoney.value = true;
      } else {
        canBackMoney.value = false;
      }
      canRefundMoney.value = orderAmt - allRefundOrderAmt;
      if (orderBillDetailResult.value.settleSign == 1 &&
          canRefundMoney.value > Decimal.zero) {
        canBackMoney.value = true;
      } else if (orderBillDetailResult.value.settleSign == 2) {
        canRefundMoney.value = Decimal.zero;
        canBackMoney.value = false;
      }

      if (orderBillDetailResult.value.orderType.toString() ==
              OrderType.preApi.type ||
          orderBillDetailResult.value.orderType.toString() ==
              OrderType.otherScanApi.type ||
          orderBillDetailResult.value.orderType.toString() ==
              OrderType.api.type) {
        canBackMoney.value = false;
      }
    } else {
      NewPayAlert.showFailureWithMessage(result.message ?? '');
    }
    // canBackMoney.value = true;
  }

  Future<void> goBackMoney() async {
    final inputRefundAmountResult = await ConfirmAlterCenter.showRefundDialog(
      refundAmount: canRefundMoney.value,
      // refundAmount: Decimal.parse('100'),
      currency: orderBillDetailResult.value.payCurrency ?? 'USD',
      // currency: 'LAK',
    );
    if (inputRefundAmountResult != null &&
        inputRefundAmountResult != Decimal.zero) {
      // 退款
      final createRefundResult = await MerchantApi().backMoneyBillCreate(
        orderBillDetailResult.value.id.toString(),
        inputRefundAmountResult.toString(),
      );
      if (createRefundResult.isSuccess) {
        final data = createRefundResult.data as Map<String, dynamic>?;
        final detail = MerchantOrderRefundCreateResult.fromJson(data ?? {});
        refundOrderNo.value = detail.refundOrderNo;
        //弹出确认是否退款
        final isSure = await ConfirmAlterCenter.showConfirmDialog(
          title: 'refund'.tr,
          content: 'refund_confirm_content'.trParams({
            'refundAmount': inputRefundAmountResult.toString(),
            'refundCurrency': orderBillDetailResult.value.payCurrency ?? 'USD',
          }),
          confirmText: 'confirm_refund_title'.tr,
          cancelText: 'cancel'.tr,
        );
        if (isSure) {
          //退款
          final isSureResult = await MerchantApi().backMoneySure(
            refundOrderNo.value,
          );
          if (isSureResult.isSuccess) {
            //退款成功
            NewPayAlert.showSuccessWithMessage('refund_success'.tr);
            loadOrderDetail();
          } else {
            NewPayAlert.showFailureWithMessage(isSureResult.message ?? '');
          }
        } else {
          final isCancelResult = await MerchantApi().backMoneyCancel(
            refundOrderNo.value,
          );
          if (!isCancelResult.isSuccess) {
            NewPayAlert.showFailureWithMessage(isCancelResult.message ?? '');
          }
        }
      } else {
        NewPayAlert.showFailureWithMessage(createRefundResult.message ?? '');
      }
    }
  }
}
