import 'package:flnewpay_core/api/models/merchant/order_bill_detail_result.dart';
import 'package:flnewpay_core/api/models/newpaydefine.dart';
import 'package:flnewpay_core/common/utils/flnewglobalpayconst.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flnewpay_core/common/widgets/merchant_app_bar.dart';

import 'package:qr_flutter/qr_flutter.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'controller.dart';

//订单详情页面
class MerchantOrderDetailPage extends GetView<MerchantOrderDetailController> {
  const MerchantOrderDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: FlNewAppColors.white,
      appBar: MerchantAppBar(title: 'order_detail'.tr),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Obx(
      () => CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: controller.isRefreshing.value,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    alignment: Alignment.center,
                    child: Column(
                      children: [
                        if (controller.orderBillDetailResult.value.payMethod !=
                            null)
                          _buildPayMethodIcon(
                            controller.orderBillDetailResult.value.payMethod,
                          ),
                      ],
                    ),
                  ),
                  // 订单信息列表
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        _buildInfoItem(
                          'order_number'.tr,
                          controller.orderNo.value,
                        ),
                        _buildInfoItem(
                          'order_status'.tr,
                          _getOrderStatus(
                            controller
                                .orderBillDetailResult
                                .value
                                .orderStatusMerchant,
                          ),
                        ),
                        _buildInfoItem(
                          'pay_time'.tr,
                          _formatTimestamp(
                            controller.orderBillDetailResult.value.payTime,
                          ),
                        ),
                        _buildInfoItem(
                          'order_subject'.tr,
                          controller.orderBillDetailResult.value.subject ?? '',
                        ),

                        _buildInfoItem(
                          'order_amount'.tr,
                          '${controller.orderBillDetailResult.value.orderAmtDisplay} ${controller.orderBillDetailResult.value.payCurrency ?? 'USD'}',
                        ),
                        _buildInfoItem(
                          'order_settlement_status'.tr,
                          controller.orderBillDetailResult.value
                              .getSettlementStatusText()
                              .$1,
                        ),
                        // _buildInfoItem(
                        //   'actual_amount'.tr,
                        //   '${controller.orderBillDetailResult.value.actualAmountDisplay} ${controller.orderBillDetailResult.value.payCurrency ?? 'USD'}',
                        // ),
                        _buildInfoItem(
                          'order_paycustomer_name'.tr,
                          controller.orderBillDetailResult.value.userNickName ??
                              '-',
                        ),
                        _buildRefundList(
                          controller
                              .orderBillDetailResult
                              .value
                              .refundListVoList,
                        ),
                      ],
                    ),
                  ),
                  // 二维码
                  Container(
                    padding: const EdgeInsets.all(20),
                    alignment: Alignment.center,
                    child: _buildQRCode(controller.orderNo.value),
                  ),
                  // 退款按钮
                  Obx(
                    () =>
                        controller.canBackMoney.value
                            ? Container(
                              padding: const EdgeInsets.all(16),
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () => controller.goBackMoney(),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: FlNewAppColors.primaryGreen,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'refund'.tr,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            )
                            : const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ),
          // 支付方式图标和名称
        ],
      ),
    );
  }

  /// 构建单条退款信息
  Widget _buildRefundItem(RefundListVoList refund) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoItem(
            'order_refund_amount'.tr,
            refund.refundOrderAmtDisplay(),
          ),
          _buildInfoItem(
            'order_refund_time'.tr,
            _formatTimestamp(refund.refundApplyTime),
          ),
        ],
      ),
    );
  }

  Widget _buildRefundList(List<RefundListVoList>? refundListVoList) {
    if (refundListVoList == null || refundListVoList.isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            'refund_record'.tr,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
        ),
        ...refundListVoList.map((e) => _buildRefundItem(e)),
      ],
    );
  }

  Widget _buildPayMethodIcon(int? payMethod) {
    String? iconPath = NewPayMethod.getIcondetailPath(payMethod ?? -1);
    return iconPath != null
        ? Image.asset(
          iconPath,
          width: 70,
          height: 70,
          package: 'flnewpay_core',
          errorBuilder:
              (context, error, stackTrace) =>
                  const Icon(Icons.error, size: 48, color: Colors.red),
        )
        : Text('${payMethod}');
  }

  String _getPayMethodName(int? payMethod) {
    return NewPayMethod.getDisplayName(payMethod ?? 0);
  }

  String _getOrderStatus(int? status) {
    if (status == 2) {
      return 'order_refunded'.tr;
    } else if (status == 1) {
      return 'order_finished'.tr;
    } else {
      return '$status';
    }
  }

  String _formatTimestamp(int? timestamp) {
    if (timestamp == null) return '-';
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} '
        '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}';
  }

  String _getOrderType(int? type) {
    switch (type) {
      case 1:
        return 'collection_code'.tr;
      case 2:
        return 'collection'.tr;
      case 3:
        return 'api_payment'.tr;
      default:
        return '-';
    }
  }

  Widget _buildQRCode(String orderNo) {
    return QrImageView(data: orderNo, size: 120);
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }
}
