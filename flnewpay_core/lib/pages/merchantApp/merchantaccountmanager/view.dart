import 'package:flnewpay_core/api/models/merchant/merchant_account_info_result.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';
import 'package:flnewpay_core/common/utils/flnewglobalpayconst.dart';
import 'package:flnewpay_core/common/widgets/empty_data_view.dart';
import 'package:flnewpay_core/common/widgets/merchant_app_bar.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantaccountmanager/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:visibility_detector/visibility_detector.dart';

class MerchantaccountmanagerPage
    extends GetView<MerchantaccountmanagerController> {
  const MerchantaccountmanagerPage({super.key});

  // 主视图
  Widget _buildView(MerchantaccountmanagerController controller) {
    return SmartRefresher(
      controller: controller.refreshController,
      header: const WaterDropHeader(complete: Text('')),
      footer: CustomFooter(
        builder: (context, mode) {
          if (mode == LoadStatus.noMore) {
            return EmptyStateWidget(title: 'nomoredata'.tr);
          }
          return const SizedBox.shrink();
        },
      ),
      onRefresh: controller.loadData,
      onLoading: controller.loadMore,
      enablePullUp: true,
      enablePullDown: true,

      child:
          controller.allAccounts.isEmpty
              ? ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [EmptyStateWidget(title: 'nomoredata'.tr)],
              )
              : ListView.builder(
                itemCount: controller.allAccounts.length,
                itemBuilder:
                    (context, index) =>
                        _buildItem(controller.allAccounts[index]),
              ),
    );
  }

  Widget _buildItem(AccountItemInfo item) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.accountName ?? '',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (item.shopName != null && item.shopName!.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(top: 4.h),
                    child: Text(
                      item.shopName!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Column(
            children: [
              if (MerchantUser.instance.isMerchant())
                IconButton(
                  onPressed: () {
                    controller.showMoreDialog(item);
                  },
                  icon: const Icon(
                    Icons.more_horiz,
                    color: FlNewAppColors.primaryGreen,
                  ),
                ),
              !item.isMain()
                  ? GetBuilder<MerchantaccountmanagerController>(
                    id: 'merchantaccount${item.id}',
                    builder: (_) {
                      return Switch(
                        value: item.accountStatus == 1,
                        onChanged: (_) => controller.showConfirmDialog(item),
                        activeColor: const Color(0xFF00A29E),
                        activeTrackColor: const Color(0xFF00A29E),
                        thumbColor: const WidgetStatePropertyAll(Colors.white),
                        inactiveThumbColor: const Color(0xFF00A29E),
                      );
                    },
                  )
                  : const SizedBox.shrink(),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MerchantaccountmanagerController>(
      id: "merchantaccountmanager",
      builder: (_) {
        return VisibilityDetector(
          key: const Key('merchantaccountmanager'),
          onVisibilityChanged: (visibilityInfo) {
            if (visibilityInfo.visibleFraction == 1) {
              controller.loadData();
            }
          },
          child: Scaffold(
            appBar: MerchantAppBar(
              title: "merchantaccountmanager".tr,
              actions: [
                if (MerchantUser.instance.isMerchant())
                  IconButton(
                    onPressed: controller.addAccount,
                    icon: const Icon(Icons.add, color: Colors.white),
                  ),
              ],
            ),
            body: _buildView(controller),
          ),
        );
      },
    );
  }
}
