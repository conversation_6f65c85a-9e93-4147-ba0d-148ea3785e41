import 'package:flnewpay_core/api/models/merchant/merchant_wallet_result.dart';
import 'package:flnewpay_core/api/models/merchant/query_account_info.dart';
import 'package:flnewpay_core/common/routes/app_pages.dart';
import 'package:flnewpay_core/common/utils/flnewpayalter.dart';
import 'package:flnewpay_core/common/utils/route_utils.dart';
import 'package:flnewpay_core/common/utils/online_card_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flnewpay_core/api/merchant_api.dart';

class MerchantTransferIdCheckController extends GetxController {
  final MerchantApi _merchantApi = MerchantApi();
  final focusNode = FocusNode();
  final isKeyboardVisible = false.obs;
  final transfer = QueryWalletInfo().obs;
  // 卡号输入控制器
  final cardNumberController = TextEditingController();
  // 卡号
  final cardNumber = ''.obs;
  // 最近使用的卡号列表
  final recentCards = <Map<String, dynamic>>[].obs;

  // 最大卡号长度
  static const int maxCardLength = 16;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments['transfer'] != null) {
      transfer.value = Get.arguments['transfer'];
    }
    // 监听输入变化
    cardNumberController.addListener(() {
      cardNumber.value = cardNumberController.text;
    });

    // 加载最近使用的卡号列表
    loadRecentCards();
  }

  @override
  void onClose() {
    cardNumberController.dispose();
    focusNode.dispose();
    super.onClose();
  }

  /// 加载最近使用的卡号列表
  Future<void> loadRecentCards() async {
    final cards = await OnlineCardManager.getRecentCards();
    recentCards.assignAll(cards);
  }

  /// 选择最近使用的卡号
  void selectRecentCard(String cardNumber) {
    this.cardNumber.value = cardNumber;
    cardNumberController.text = cardNumber;
    onNextStep();
  }

  /// 删除最近使用的卡号
  Future<void> deleteRecentCard(String cardNumber) async {
    await OnlineCardManager.deleteRecentCard(cardNumber);
    await loadRecentCards(); // 重新加载列表
  }

  // 下一步
  void onNextStep() async {
    if (cardNumber.value.isEmpty) {
      NewPayAlert.showTip(title: 'enter_card_number'.tr, message: ''.tr);
      return;
    }

    final response = await _merchantApi.queryRevUserInfoById(cardNumber.value);

    if (response.isSuccess && response.data != null) {
      // 获取商户信息
      final cardAccountInfo = response.data as Map<String, dynamic>;
      final cardAccountInfoResult = QueryTransferAccountInfo.fromJson(
        cardAccountInfo,
      );

      // 保存到最近使用的卡号列表
      await OnlineCardManager.addRecentCard(
        cardNumber.value,
        cardAccountInfoResult.revName ?? '',
      );

      // 刷新最近使用的卡号列表
      await loadRecentCards();

      RouteUtils.toNamed(
        AppPages.merchantTransferConfirm,
        arguments: {'transferAccountInfo': cardAccountInfoResult},
      );
    } else {
      NewPayAlert.showTip(
        title: 'error'.tr,
        message: response.message ?? 'invalid_card_number'.tr,
      );
    }
  }
}
