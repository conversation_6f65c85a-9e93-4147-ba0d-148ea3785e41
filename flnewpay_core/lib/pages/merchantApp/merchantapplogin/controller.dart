import 'dart:convert';

import 'package:flnewpay_core/api/models/merchant/query_shop_list_bean.dart';
import 'package:flnewpay_core/common/extensions/md5_str.dart';
import 'package:flnewpay_core/common/utils/flnewdeviceinfo.dart';
import 'package:flnewpay_core/common/utils/flnewglobalpayconst.dart';
import 'package:flnewpay_core/common/utils/flnewpay_loading.dart';
import 'package:flnewpay_core/common/utils/flnewpayalter.dart';
import 'package:flnewpay_core/common/utils/log.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flnewpay_core/api/merchant_api.dart';
import 'package:flnewpay_core/common/routes/app_pages.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flnewpay_core/api/models/merchant/login_req.dart';
import 'package:flnewpay_core/api/api_exception.dart';
import 'package:flnewpay_core/api/models/merchant/login_bean_result.dart';
import 'package:flnewpay_core/common/utils/route_utils.dart';
import 'package:flnewpay_core/common/services/language_service.dart';

class MerchantAppLoginController extends GetxController {
  // 用户名和密码的控制器
  late TextEditingController usernameController;
  late TextEditingController passwordController;
  // 可观察的状态变量
  final isPrivacyChecked = false.obs;
  final isPasswordVisible = false.obs;
  final isLoginEnabled = false.obs;
  final hasShownPrivacyDialog = false.obs;

  // 用户名和密码的值
  final username = ''.obs;
  final password = ''.obs;

  // 隐私协议URL
  final privacyPolicyUrl = ''.obs;

  //用户协议
  final userAgreementUrl = ''.obs;

  // 隐私协议SP键名
  static const String PRIVACY_IS_OK = 'privacy_is_ok';

  // 当前语言
  final currentLanguage = ''.obs;

  // 动画开关
  final RxBool isAnimationEnabled = false.obs;

  @override
  void onInit() {
    super.onInit();

    usernameController = TextEditingController();
    passwordController = TextEditingController();
    privacyPolicyUrl.value = 'https://prod-h5.newpay.la/xy/yszc.html';
    userAgreementUrl.value = 'https://prod-h5.newpay.la/xy/userProtocol.html';
    currentLanguage.value = LanguageService.to.currentLocale.languageCode;
    ever(LanguageService.to.localeStream, (locale) {
      Log.v('语言切换监听触发: $locale');
      currentLanguage.value = locale.languageCode;
      Log.v('准备更新UI，controller hash: ${identityHashCode(this)}');
      update();
      Log.v('UI更新完成');
    });
  }

  @override
  void onReady() {
    super.onReady();
    usernameController.text = username.value;
    passwordController.text = password.value;
    usernameController.addListener(_usernameListener);
    passwordController.addListener(_passwordListener);

    _getPrivacyAgreement().then((agreed) {
      isPrivacyChecked.value = agreed;
      _validateInputs();

      if (!agreed && !hasShownPrivacyDialog.value) {
        hasShownPrivacyDialog.value = true;
        Future.delayed(const Duration(milliseconds: 500), () {
          if (Get.isRegistered<MerchantAppLoginController>() &&
              Get.currentRoute == AppPages.merchantLogin) {
            showPrivacyDialog();
          }
        });
      }
    });
  }

  @override
  void onClose() {
    Log.v(
      'MerchantAppLoginController onClose. Hash: ${identityHashCode(this)}',
    );

    usernameController.removeListener(_usernameListener);
    passwordController.removeListener(_passwordListener);
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  void _usernameListener() {
    username.value = usernameController.text;
    _validateInputs();
  }

  void _passwordListener() {
    password.value = passwordController.text;
    _validateInputs();
  }

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  Future<String> _getDeviceInfo() async {
    final deviceInfoService = DeviceInfoService();
    final deviceInfo = await deviceInfoService.buildDeviceInfo();
    return jsonEncode(deviceInfo);
  }

  void togglePrivacyCheck() {
    isPrivacyChecked.value = !isPrivacyChecked.value;
    if (isPrivacyChecked.value) {
      _savePrivacyAgreement(true);
    }
    _validateInputs();
  }

  void _validateInputs() {
    isLoginEnabled.value =
        username.value.isNotEmpty &&
        password.value.isNotEmpty &&
        isPrivacyChecked.value;
  }

  Future<void> login() async {
    if (Get.context != null && FocusScope.of(Get.context!).hasFocus) {
      FocusScope.of(Get.context!).unfocus();
    }
    if (!isLoginEnabled.value) return;
    if (!isPrivacyChecked.value) {
      FlNewPayLoading.showError('请先同意隐私协议'.tr);
      return;
    }

    FlNewPayLoading.show('登录中...'.tr);
    final loginReq = LoginReq(
      username: username.value,
      password: password.value.getMd5Val(),
      ipAddress: '',
      deviceInfo: await _getDeviceInfo(),
    );
    final response = await MerchantApi().login(loginReq);
    if (response.isSuccess) {
      final loginData = LoginBeanResult.fromJson(response.data);
      await _handleLoginData(loginData);
      if (MerchantUser.instance.userType == '1') {
        await _getShopList();
      }
      await RouteUtils.offAllNamed(AppPages.merchantRoot);
    } else {
      FlNewPayLoading.dismiss();
      FlNewPayLoading.showError(response.message ?? 'failed_login_error'.tr);
    }
  }

  Future<void> _getShopList() async {
    final response = await MerchantApi().queryStoreMerchantList();
    if (response.isSuccess && response.data != null) {
      final List<QueryShopListBean> newList =
          (response.data as List)
              .map((e) => QueryShopListBean.fromJson(e))
              .toList();
      if (newList.isNotEmpty) {
        final merchantShop = newList.firstWhereOrNull((e) => e.shopType == '1');
        await MerchantUser.instance
          ..shopId = merchantShop?.shopId
          ..localBusiName = merchantShop?.localShopName;
        await MerchantUser.instance.saveself();
      }
    }
  }

  Future<void> _handleLoginData(LoginBeanResult loginData) async {
    if (loginData.accountTokenVO?.accessToken == null) {
      throw ApiException(message: 'failed_login_data_null'.tr, code: 'ERROR');
    }
    MerchantUser.initLoginData(loginData);
    await MerchantUser.instance.saveself();
  }

  void _handleLoginError(dynamic e) {
    Log.v('登录错误: $e');
    if (e is ApiException) {
      final errorCodes = ['U10025', 'W10136', 'W10133', 'W10134'];
      if (errorCodes.contains(e.code)) {
        MerchantUser.instance.clearAndSave();
      }
      FlNewPayLoading.showError(e.message);
    } else {
      FlNewPayLoading.showError('${e.toString()}failed_login_error'.tr);
    }
  }

  RichText getPrivacyText(BuildContext context) {
    const labelStyle = TextStyle(fontSize: 12, color: Colors.black87);
    const linkStyle = TextStyle(
      fontSize: 12,
      color: FlNewAppColors.primaryGreen,
      decoration: TextDecoration.underline,
    );

    return RichText(
      text: TextSpan(
        style: labelStyle,
        children: [
          TextSpan(text: 'privacy_login_agreement'.tr),
          TextSpan(
            text: 'privacy_policy'.tr,
            style: linkStyle,
            recognizer:
                TapGestureRecognizer()
                  ..onTap = () {
                    openWebView(privacyPolicyUrl.value, 'privacy_policy'.tr);
                  },
          ),
        ],
      ),
    );
  }

  void openWebView(String url, String title) async {
    RouteUtils.toNamed(
      AppPages.merchantWebview,
      arguments: {'url': url, 'title': title},
    );
  }

  Future<bool> _getPrivacyAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(PRIVACY_IS_OK) ?? false;
  }

  Future<void> _savePrivacyAgreement(bool value) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool(PRIVACY_IS_OK, value);
  }

  void showPrivacyDialog() {
    PrivacyAlterCenter.showPrivacyDialog(
      privacyPolicyUrl: privacyPolicyUrl.value,
      useragreementUrl: userAgreementUrl.value,
      onAgree: () {
        togglePrivacyCheck();
      },
      onDisagree: () {
        _showQuitDialog();
      },
      onOpenWebView: (url, title) {
        openWebView(url, title);
      },
    );
  }

  void _showQuitDialog() {
    PrivacyAlterCenter.showQuitDialog(
      onQuit: () {
        exitApp();
      },
      onGoToAgree: () {
        Get.back();
        showPrivacyDialog();
      },
    );
  }

  void exitApp() {
    MerchantUser.instance.clearAndSave();
    RouteUtils.offAllNamed(AppPages.merchantRoot);
  }

  Future<void> changeLanguage(String languageCode, String countryCode) async {
    await LanguageService.to.updateLanguage(Locale(languageCode, countryCode));
  }

  void toggleAnimation() {
    isAnimationEnabled.value = !isAnimationEnabled.value;
  }
}
