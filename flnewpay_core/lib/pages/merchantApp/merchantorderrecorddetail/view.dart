import 'package:decimal/decimal.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_wallet_record_detail.dart';
import 'package:flnewpay_core/common/extensions/date_time_ex.dart';
import 'package:flnewpay_core/common/theme/colors.dart';
import 'package:flnewpay_core/common/utils/decimalutilformat.dart';
import 'package:flnewpay_core/common/utils/flnewglobalpayconst.dart';
import 'package:flnewpay_core/common/utils/order_type_utils.dart';
import 'package:flnewpay_core/common/widgets/widget_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'controller.dart';
import 'package:flnewpay_core/common/widgets/merchant_app_bar.dart';

class MerchantOrderRecordDetailPage
    extends GetView<MerchantOrderRecordDetailController> {
  const MerchantOrderRecordDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MerchantAppBar(title: 'order_record_detail'.tr),
      body: GetBuilder<MerchantOrderRecordDetailController>(
        id: 'detail',
        builder: (controller) {
          return _buildDetailView(controller);
        },
      ),
    );
  }

  Widget _buildDetailView(MerchantOrderRecordDetailController controller) {
    // 类型、金额、商户名等处理
    final MerchantWalletRecordDetail? detail = controller.detail.value;
    final int type = detail?.orderType ?? 0;
    IconData iconData = OrderTypeUtils.getTypeIcon(type);
    // String typeName = OrderTypeUtils.getTypeName(type);
    String tipName = OrderTypeUtils.getTipName(type);
    String topName = '';
    String payType = 'NewPay';
    String amountStr = '';
    String orderDesc = detail?.orderDesc ?? '';
    String orderNo = detail?.orderNo.toString() ?? '';
    String timeStr = '';
    bool showPayType = true;
    bool showPostscript = true;
    bool showReceipt = false;
    bool showTopValue = true;

    // 金额符号和格式
    bool isAdd = OrderTypeUtils.isAddAmount(type);
    Color amountColor = OrderTypeUtils.getAmountColor(type);

    // 银行卡提现
    final isWithdrawBank =
        detail?.orderType == 1 && detail?.cardNumberLast != null;

    switch (type) {
      case 1: // 商户提现
        topName =
            isWithdrawBank
                ? '${detail?.bankName ?? ''}(${detail?.cardNumberLast})'
                : detail?.toNickName ?? '';
        showPayType = false;
        showPostscript = false;
        showReceipt = false;
        showTopValue = true;
        break;
      case 2: // 商户转账-转入
        topName = detail?.fromNickName ?? '';
        showPayType = true;
        showPostscript = true;
        showReceipt = false;
        showTopValue = true;
        break;
      case 3: // 商户结算
        topName = detail?.fromNickName ?? controller.SETTLE_SHOP_NAME;
        showPayType = false;
        showPostscript = false;
        showReceipt = false;
        showTopValue = true;
        break;
      case 4: // 商户转账-转出
        topName = detail?.toNickName ?? '';
        showPayType = true;
        showPostscript = true;
        showReceipt = true;
        showTopValue = true;
        break;
      case 5: // 商户发工资
        topName = detail?.toNickName ?? '';
        showPayType = false;
        showPostscript = false;
        showReceipt = false;
        showTopValue = false;
        break;
      default:
        topName = '';
        showPayType = false;
        showPostscript = false;
        showReceipt = false;
        showTopValue = true;
    }
    String formatamount =
        (detail?.currency ?? 'USD') == 'LAK'
            ? DecimalUtilFormat.doubleMoneyString(
              Decimal.parse(detail?.orderAmt.toString() ?? '0'),
            )
            : DecimalUtilFormat.double2MoneyString00(
              Decimal.parse(detail?.orderAmt.toString() ?? '0.00'),
            );
    String formattedFee =
        (detail?.currency ?? 'USD') == 'LAK'
            ? DecimalUtilFormat.doubleMoneyString(
              Decimal.parse(detail?.serviceCost?.toString() ?? '0'),
            )
            : DecimalUtilFormat.double2MoneyString00(
              Decimal.parse(detail?.serviceCost?.toString() ?? '0.00'),
            );
    // 金额格式
    amountStr = '${isAdd ? '+' : '-'}$formatamount ${detail?.currency ?? ''}';
    // 时间格式
    if (detail?.orderTime != null) {
      timeStr = DateTimeExtension.formatTimestampByYYMMDDHHMMSS(
        detail?.orderTime.toString() ?? '',
      );
    }

    // 订单类型
    // orderTypeStr = typeName;

    String withdrawStateName = OrderTypeUtils.getWithdrawStateName(
      detail?.recordSate,
    );
    return SmartRefresher(
      controller: controller.refreshController,
      onRefresh: controller.onRefresh,
      header: const WaterDropHeader(
        waterDropColor: FlNewAppColors.primaryGreen,
        complete: Text(''),
        failed: Text(''),
      ),
      child: SingleChildScrollView(
        child: Skeletonizer(
          enabled: controller.detail.value == null,
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(iconData, color: FlNewAppColors.primaryGreen, size: 72),
                if (isWithdrawBank)
                  Padding(
                    padding: EdgeInsets.only(top: 12.h),
                    child: Text(
                      'order_withdraw_detail_title'.trArgs([
                        detail?.cardNumberLast ?? '',
                      ]),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: NewPayColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                // 金额
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  child: Text(
                    amountStr,
                    style: TextStyle(
                      // color: amountColor,
                      color: Colors.black,
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (isWithdrawBank)
                  Padding(
                    padding: EdgeInsets.only(bottom: 12.h),
                    child: Text(
                      withdrawStateName.isEmpty ? 'Unknown' : withdrawStateName,
                      style: TextStyle(
                        color: OrderTypeUtils.getWithdrawStateColor(
                          detail?.recordSate,
                        ),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                1.hDivider(),
                12.vGap(),
                // 交易时间
                _buildDetailRow(
                  isWithdrawBank
                      ? 'order_withdraw_detail_create_time_title'.tr
                      : 'transaction_time'.tr,
                  timeStr,
                ),
                if (showTopValue && topName.isNotEmpty)
                  // 顶部类型和商户名
                  _buildDetailRow(tipName, topName),
                if (isWithdrawBank && detail?.progressList != null)
                  // 处理进度
                  _buildProgress(detail?.progressList ?? []),
                if (isWithdrawBank)
                  _buildDetailRow(
                    'order_withdraw_detail_actual_deduct_amount_title'.tr,
                    detail?.orderAmt == null ||
                            detail?.serviceCost == null ||
                            detail?.currency == null
                        ? '0 ${detail?.currency ?? ''}'
                        : '${_calculateActualAmount(detail?.orderAmt, detail?.serviceCost, detail?.currency)} ${detail?.currency ?? ''}',
                  ),
                if (isWithdrawBank)
                  // 手续费
                  _buildDetailRow(
                    'order_withdraw_detail_fee_title'.tr,
                    '$formattedFee ${detail?.currency ?? ''}',
                  ),
                // 支付方式
                if (showPayType) _buildDetailRow('payment_method'.tr, payType),
                // 类型
                // _buildDetailRow('type'.tr, orderTypeStr),
                // 订单号
                _buildDetailRow('order_no'.tr, orderNo),
                // 转账附言
                if (showPostscript)
                  _buildDetailRow('transfer_remark'.tr, orderDesc),
                16.vGap(),
                // 回执单按钮
                if (showReceipt)
                  Card(
                    child: ListTile(
                      title: Text('view_receipt'.tr),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: controller.onViewReceipt,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 计算实际到账金额
  String _calculateActualAmount(
    String? orderAmt,
    double? serviceCost,
    String? currency,
  ) {
    if (orderAmt == null || serviceCost == null || currency == null) return '0';

    try {
      final amount =
          Decimal.parse(orderAmt) - Decimal.parse(serviceCost.toString());
      return currency == 'LAK'
          ? DecimalUtilFormat.doubleMoneyString(amount)
          : DecimalUtilFormat.double2MoneyString00(amount);
    } catch (e) {
      return '0';
    }
  }

  String _getTimeStr(MerchantWalletRecordDetailProgress progress) {
    String timeStr = '';

    if (progress.time != null) {
      timeStr = DateTimeExtension.formatTimestampByYYMMDDHHMMSS(
        progress.time.toString(),
      );
    }
    if (progress.name == 'loading') {
      timeStr = 'order_withdraw_state_detail_processing_time'.trParams({
        'time': timeStr,
      });
    }
    return timeStr;
    // return '${dt.year}/${dt.month.toString().padLeft(2, '0')}/${dt.day.toString().padLeft(2, '0')} '
    //     '${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}:${dt.second.toString().padLeft(2, '0')}';
  }

  Widget _buildProgress(List<MerchantWalletRecordDetailProgress> progressList) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              'order_withdraw_state_detail_title'.tr,
              maxLines: null,
              overflow: TextOverflow.visible,
              style: TextStyle(
                color: NewPayColors.textPrimary,
                fontSize: 14.sp,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 12.h,
              children: List.generate(progressList.length, (i) {
                final progress = progressList[i];
                final isCurrent =
                    progress.time != null &&
                    ((i != progressList.length - 1 &&
                            progressList[i + 1].time == null) ||
                        i == progressList.length - 1);
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      OrderTypeUtils.getWithdrawProgressName(progress.name),
                      style: TextStyle(
                        color:
                            isCurrent
                                ? NewPayColors.textPrimary
                                : NewPayColors.textDisabled,
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                    if (progress.time != null)
                      Padding(
                        padding: EdgeInsets.only(top: 2.h),
                        child: Text(
                          _getTimeStr(progress),
                          style: TextStyle(
                            fontSize: 12.sp,
                            color:
                                isCurrent
                                    ? NewPayColors.textPrimary
                                    : NewPayColors.textDisabled,
                          ),
                        ),
                      ),
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              maxLines: null,
              overflow: TextOverflow.visible,
              style: TextStyle(
                color: NewPayColors.textPrimary,
                fontSize: 14.sp,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                Clipboard.setData(ClipboardData(text: value));
                Get.snackbar(
                  ''.tr,
                  '$value ${'copied'.tr}',
                  snackPosition: SnackPosition.BOTTOM,
                );
              },
              child: Text(
                value,
                style: TextStyle(
                  color: valueColor ?? NewPayColors.textPrimary,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
