class MerchantBankListPage extends GetView<MerchantBankListController> {
  const MerchantBankListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MerchantBankListController>(
      builder: (controller) {
        return Scaffold(
          appBar: MerchantAppBar(title: 'select_bank'.tr),
          body: SafeArea(child: _buildBody()),
        );
      },
    );
  }

  Widget _buildBody() {
    return SmartRefresher(
      controller: controller.refreshController,
      onRefresh: controller.onRefresh,
      onLoading: controller.loadMore,
      header: const WaterDropHeader(
        waterDropColor: FlNewAppColors.primaryGreen,
        complete: Text(''),
        failed: Text(''),
      ),
      footer: CustomFooter(
        builder: (context, mode) {
          if (mode == LoadStatus.noMore) {
            return EmptyStateWidget(title: 'nomoredata'.tr);
          }
          return const SizedBox.shrink();
        },
      ),
      enablePullUp: true,
      enablePullDown: true,
      child: ListView.builder(
        itemCount: controller.bankList.length,
        itemBuilder: (context, index) {
          final BankOrganizationInfo item = controller.bankList[index];
          return BankItem(item).onTap(() => controller.goToBind(index));
        },
      ),
    );
  }
}
