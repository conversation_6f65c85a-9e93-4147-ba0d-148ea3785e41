// import 'package:flnewpay_core/common/widgets/empty_data_view.dart';
// import 'package:flnewpay_core/common/widgets/merchant_app_bar.dart';
// import 'package:flutter/material.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';
// import 'package:get/get.dart';

// class MerchantBankListPage extends GetView<MerchantBankListController> {
//   const MerchantBankListPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<MerchantBankListController>(
//       builder: (controller) {
//         return Scaffold(
//           appBar: MerchantAppBar(title: 'select_bank'.tr),
//           body: SafeArea(child: _buildBody(controller)),
//         );
//       },
//     );
//   }

//   Widget _buildBody(MerchantBankListController controller) {
//     return SmartRefresher(
//       controller: controller.refreshController,
//       onRefresh: controller.onRefresh,
//       onLoading: controller.loadMore,
//       header: const WaterDropHeader(
//         waterDropColor: FlNewAppColors.primaryGreen,
//         complete: Text(''),
//         failed: Text(''),
//       ),
//       footer: CustomFooter(
//         builder: (context, mode) {
//           if (mode == LoadStatus.noMore) {
//             return EmptyStateWidget(title: 'nomoredata'.tr);
//           }
//           return const SizedBox.shrink();
//         },
//       ),
//       enablePullUp: true,
//       enablePullDown: true,
//       child: ListView.builder(
//         itemCount: controller.bankList.length,
//         itemBuilder: (context, index) {
//           final BankOrganizationInfo item = controller.bankList[index];
//           return BankItem(item).onTap(() => controller.goToBind(index));
//         },
//       ),
//     );
//   }
// }
