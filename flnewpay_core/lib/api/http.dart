import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flnewpay_core/api/interceptors/log_interceptor.dart';
import 'package:flnewpay_core/api/interceptors/merchant_headers_interceptor.dart';
import 'package:flnewpay_core/common/app_config.dart';
import 'package:flnewpay_core/common/utils/flnewpay_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' as getx;
import 'api_exception.dart';
import 'api_config.dart';
import 'package:flnewpay_core/api/interceptors/merchant_token_interceptor.dart';
import 'models/api_reponse.dart';
import 'package:flnewpay_core/common/utils/log.dart';

/// 缓存策略
enum CachePolicy {
  /// 只从网络获取数据，且数据不会缓存在本地
  ignoreCache,

  /// 只从缓存读数据，如果缓存没有数据，返回一个空
  cacheOnly,

  /// 先从网络获取数据，同时会在本地缓存数据
  networkOnly,

  /// 先从缓存读取数据，如果没有再从网络获取
  cacheElseNetwork,

  /// 先从网络获取数据，如果没有再从缓存读取数据
  networkElseCache,

  /// 先从缓存读取数据，然后再从网络获取数据，Block将产生两次调用
  cacheThenNetwork,
}

/// 请求方法
enum RequestMethod { get, post, head, put, patch, delete }

class Http {
  static final Http _instance = Http._internal();
  factory Http() => _instance;
  late Dio dio;
  bool _logEnabled = true;
  String? _baseURL;
  Map<String, dynamic>? _baseParameters;
  List<String>? _filtrationCacheKey;
  final List<CancelToken> _allSessionTask = [];
  final _dataCache = <String, dynamic>{};

  Http._internal() {
    final config = ApiConfig();
    dio = Dio(
      BaseOptions(
        baseUrl: config.baseUrl,
        connectTimeout: const Duration(seconds: ApiConfig.connectTimeout),
        receiveTimeout: const Duration(seconds: ApiConfig.receiveTimeout),
        headers: config.headers,
      ),
    );

    if (AppConfig().appType == AppType.merchant) {
      dio.interceptors.add(MerchantHeadersInterceptor());
      dio.interceptors.add(MerchantTokenInterceptor());
    }
    // 添加拦截器
    dio.interceptors.add(MerchantLogInterceptor());
  }

  void initAdapter() {
    dio.httpClientAdapter =
        IOHttpClientAdapter()
          ..onHttpClientCreate = (client) {
            // Config the client.
            client.findProxy = (uri) {
              // Forward all request to proxy "localhost:8888".
              return 'PROXY http://*************:8888';
            };
            // You can also create a new HttpClient for Dio instead of returning,
            // but a client must being returned here.
            return client;
          };
  }

  /// 设置是否打印日志
  void setLogEnabled(bool enabled) {
    _logEnabled = enabled;
  }

  /// 设置基础URL
  void setBaseURL(String baseURL) {
    _baseURL = baseURL;
    // 同时更新Dio实例的baseUrl
    dio.options.baseUrl = baseURL;
  }

  /// 设置基础参数
  void setBaseParameters(Map<String, dynamic> parameters) {
    _baseParameters = parameters;
  }

  /// 设置过滤缓存Key
  void setFiltrationCacheKey(List<String> keys) {
    _filtrationCacheKey = keys;
  }

  /// 设置请求头
  void setHeader(Map<String, String> header) {
    header.forEach((key, value) {
      dio.options.headers[key] = value;
    });
  }

  /// 设置Cookie
  void setCookie(String cookie) {
    dio.options.headers['Cookie'] = cookie;
  }

  /// 取消所有请求
  void cancelAllRequest() {
    for (var token in _allSessionTask) {
      token.cancel('取消请求');
    }
    _allSessionTask.clear();
  }

  /// 取消指定URL的请求
  void cancelRequestWithURL(String url) {
    if (url.isEmpty) return;
    for (var token in _allSessionTask) {
      final requestOptions = token.requestOptions;
      if (requestOptions != null) {
        final uri = requestOptions.uri;
        if (uri.toString().startsWith(url)) {
          token.cancel('取消请求');
          _allSessionTask.remove(token);
          break;
        }
      }
    }
  }

  /// 设置请求超时时间
  void setRequestTimeout(Duration timeout) {
    dio.options.connectTimeout = timeout;
    dio.options.receiveTimeout = timeout;
  }

  /// 获取缓存Key
  String _getCacheKey(String url, Map<String, dynamic>? parameters) {
    if (parameters == null) return url;

    var params = Map<String, dynamic>.from(parameters);
    if (_filtrationCacheKey != null) {
      for (var key in _filtrationCacheKey!) {
        params.remove(key);
      }
    }

    return '$url${params.toString()}';
  }

  /// 设置缓存
  void _setCache(String key, dynamic data) {
    if (data != null) {
      _dataCache[key] = data;
    }
  }

  /// 获取缓存
  dynamic _getCache(String key) {
    return _dataCache[key];
  }

  /// 清除所有缓存
  void clearCache() {
    _dataCache.clear();
  }

  /// 获取缓存大小
  int getCacheSize() {
    return _dataCache.length;
  }

  /// 处理缓存策略
  Future<ApiResponse> _handleCachePolicy(
    String url,
    Map<String, dynamic>? parameters,
    CachePolicy policy,
    Future<ApiResponse> Function() networkRequest,
  ) async {
    final cacheKey = _getCacheKey(url, parameters);

    switch (policy) {
      case CachePolicy.ignoreCache:
        return networkRequest();
      case CachePolicy.cacheOnly:
        final cachedData = _getCache(cacheKey);
        return ApiResponse.fromJson(cachedData);
      case CachePolicy.networkOnly:
        final response = await networkRequest();
        if (response.isSuccess) {
          _setCache(cacheKey, response.data);
        }
        return response;
      case CachePolicy.cacheElseNetwork:
        final cachedData = _getCache(cacheKey);
        if (cachedData != null) {
          return ApiResponse.fromJson(cachedData);
        }
        return networkRequest();
      case CachePolicy.networkElseCache:
        try {
          final response = await networkRequest();
          if (response.isSuccess) {
            _setCache(cacheKey, response.data);
          }
          return response;
        } catch (e) {
          final cachedData = _getCache(cacheKey);
          if (cachedData != null) {
            return ApiResponse.fromJson(cachedData);
          }
          rethrow;
        }
      case CachePolicy.cacheThenNetwork:
        final cachedData = _getCache(cacheKey);
        if (cachedData != null) {
          // 先返回缓存数据
          final cachedResponse = ApiResponse.fromJson(cachedData);
          // 然后发起网络请求
          networkRequest().then((response) {
            if (response.isSuccess) {
              _setCache(cacheKey, response.data);
            }
          });
          return cachedResponse;
        }
        return networkRequest();
    }
  }

  /// 处理请求参数
  Map<String, dynamic>? _handleParameters(Map<String, dynamic>? parameters) {
    final config = ApiConfig();
    final baseParams = config.baseParameters;

    if (baseParams.isEmpty) return parameters;
    if (parameters == null) return baseParams;

    final mergedParams = Map<String, dynamic>.from(baseParams);
    mergedParams.addAll(parameters);
    return mergedParams;
  }

  /// 处理URL
  String _handleURL(String url) {
    if (_baseURL == null) return url;
    return '$_baseURL$url';
  }

  /// 打印日志
  void _log(String message) {
    if (_logEnabled) {
      Log.v(message);
    }
  }

  /// GET请求
  Future<ApiResponse> get(
    String urlpath, {
    Map<String, dynamic>? parameters,
    Object? data,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic> Function(Map<String, dynamic>)? fromJson,
    CachePolicy cachePolicy = CachePolicy.ignoreCache,
    bool showLoading = true,
    String? fullUrl,
  }) async {
    try {
      if (showLoading) {
        FlNewPayLoading.show('loading'.tr);
      }
      networkRequest() async {
        try {
          final requestUrl = fullUrl != null ? '$fullUrl$urlpath' : urlpath;
          final response = await dio.get<Map<String, dynamic>>(
            requestUrl,
            data: data,
            queryParameters: parameters,
            options: options,
            cancelToken: cancelToken,
            onReceiveProgress: onReceiveProgress,
          );
          if (showLoading) {
            FlNewPayLoading.dismiss();
          }
          if (response.data != null) {
            return ApiResponse.fromJson(response.data ?? {});
          } else {
            return ApiResponse.error(message: 'No data', code: 'ERROR');
          }
        } catch (e) {
          if (showLoading) {
            FlNewPayLoading.dismiss();
          }
          if (e is DioException) {
            return _handleError(e);
          }
          throw ApiException(message: e.toString(), code: 'ERROR');
        }
      }

      return _handleCachePolicy(
        urlpath,
        parameters,
        cachePolicy,
        networkRequest,
      );
    } catch (e) {
      if (showLoading) {
        FlNewPayLoading.dismiss();
      }
      if (kDebugMode) Log.v('Get请求发生异常: $e');
      if (e is DioException) {
        return _handleError(e);
      }
      throw ApiException(message: e.toString(), code: 'ERROR');
    }
  }

  /// POST请求
  Future<ApiResponse> post(
    String urlpath, {
    dynamic data,
    Map<String, dynamic>? parameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic> Function(Map<String, dynamic>)? fromJson,
    CachePolicy cachePolicy = CachePolicy.ignoreCache,
    bool showLoading = true,
    String? fullUrl,
  }) async {
    try {
      networkRequest() async {
        if (showLoading) {
          FlNewPayLoading.show('loading'.tr);
        }
        try {
          final requestOptions = options ?? Options();

          // 构建请求URL
          String requestUrl;
          if (fullUrl != null) {
            // 如果提供了完整URL，将其与API路径组合
            requestUrl = '$fullUrl$urlpath';
          } else {
            requestUrl = urlpath;
          }
          Log.v('requestUrl: $requestUrl');
          final response = await dio.post<Map<String, dynamic>>(
            requestUrl,
            data: data,
            queryParameters: parameters,
            options: requestOptions,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );

          if (showLoading) {
            FlNewPayLoading.dismiss();
          }
          if (response.data != null) {
            return ApiResponse.fromJson(response.data ?? {});
          } else {
            return ApiResponse.error(message: 'No data', code: 'ERROR');
          }
        } catch (e) {
          if (showLoading) {
            FlNewPayLoading.dismiss();
          }
          if (e is DioException) {
            return _handleError(e);
          }
          return ApiResponse.error(message: e.toString(), code: 'ERROR');
        }
      }

      return _handleCachePolicy(
        urlpath,
        parameters,
        cachePolicy,
        networkRequest,
      );
    } on DioException catch (e) {
      if (showLoading) {
        FlNewPayLoading.dismiss();
      }
      if (kDebugMode) Log.v('Post请求发生异常: $e');
      return _handleError(e);
    } catch (e) {
      if (showLoading) {
        FlNewPayLoading.dismiss();
      }
      if (kDebugMode) Log.v('Post请求发生其他异常: $e');
      throw ApiException(message: e.toString(), code: 'ERROR');
    }
  }

  /// PUT请求
  Future<ApiResponse> put(
    String url, {
    dynamic data,
    Map<String, dynamic>? parameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic> Function(Map<String, dynamic>)? fromJson,
    CachePolicy cachePolicy = CachePolicy.ignoreCache,
  }) async {
    try {
      networkRequest() async {
        final response = await dio.put<Map<String, dynamic>>(
          url,
          data: data,
          queryParameters: parameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        );

        if (response.data != null) {
          return ApiResponse.fromJson(response.data ?? {});
        } else {
          throw ApiException(message: 'No data', code: 'ERROR');
        }
      }

      return _handleCachePolicy(url, parameters, cachePolicy, networkRequest);
    } on DioException catch (e) {
      if (kDebugMode) Log.v('Put请求发生异常: $e');
      throw _handleException(e);
    } catch (e) {
      if (kDebugMode) Log.v('Put请求发生其他异常: $e');
      throw ApiException(message: e.toString(), code: 'ERROR');
    }
  }

  /// DELETE请求
  Future<ApiResponse> delete(
    String url, {
    dynamic data,
    Map<String, dynamic>? parameters,
    Options? options,
    CancelToken? cancelToken,
    Map<String, dynamic> Function(Map<String, dynamic>)? fromJson,
    CachePolicy cachePolicy = CachePolicy.ignoreCache,
    String? fullUrl,
  }) async {
    try {
      networkRequest() async {
        // 构建请求URL
        String requestUrl;
        if (fullUrl != null) {
          // 如果提供了完整URL，将其与API路径组合
          requestUrl = '$fullUrl$url';
        } else {
          requestUrl = url;
        }
        final response = await dio.delete<Map<String, dynamic>>(
          requestUrl,
          data: data,
          queryParameters: parameters,
          options: options,
          cancelToken: cancelToken,
        );

        if (response.data != null) {
          return ApiResponse.fromJson(response.data ?? {});
        } else {
          throw ApiException(message: 'No data', code: 'ERROR');
        }
      }

      return _handleCachePolicy(url, parameters, cachePolicy, networkRequest);
    } on DioException catch (e) {
      if (kDebugMode) Log.v('Delete请求发生异常: $e');
      throw _handleException(e);
    } catch (e) {
      if (kDebugMode) Log.v('Delete请求发生其他异常: $e');
      throw ApiException(message: e.toString(), code: 'ERROR');
    }
  }

  /// 上传文件
  Future<ApiResponse> uploadFile<T>(
    String path, {
    required FormData formData,
    Options? options,
    CancelToken? cancelToken,
    void Function(int sent, int total)? onSendProgress,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    final url = _handleURL(path);
    final token = cancelToken ?? CancelToken();

    _log('上传文件: $url');

    try {
      final response = await dio.post(
        url,
        data: formData,
        options: options,
        cancelToken: token,
        onSendProgress: onSendProgress,
      );
      return ApiResponse.fromJson(response.data);
    } on DioException catch (e) {
      return _handleError(e);
    } catch (e) {
      if (e is DioException) {
        return _handleError(e);
      }

      return ApiResponse.error(message: e.toString());
    }
  }

  /// 下载文件
  Future<ApiResponse> downloadFile<T>(
    String path, {
    required String savePath,
    Options? options,
    CancelToken? cancelToken,
    void Function(int received, int total)? onReceiveProgress,
    T Function(dynamic)? fromJson,
  }) async {
    final url = _handleURL(path);
    final token = cancelToken ?? CancelToken();

    _log('下载文件: $url');

    try {
      final response = await dio.download(
        url,
        savePath,
        options: options,
        cancelToken: token,
        onReceiveProgress: onReceiveProgress,
      );
      return ApiResponse.fromJson(response.data);
    } on DioException catch (e) {
      return _handleError(e);
    } catch (e) {
      if (e is DioException) {
        return _handleError(e);
      }
      return ApiResponse.error(message: e.toString());
    }
  }

  ApiResponse _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiResponse.error(
          code: ApiException.timeout().code,
          message: ApiException.timeout().message,
        );
      case DioExceptionType.badResponse:
        return _handleResponseError(error.response);
      case DioExceptionType.cancel:
        return ApiResponse.error(message: '请求已取消');
      case DioExceptionType.connectionError:
        return ApiResponse.error(
          code: ApiException.network().code,
          message: ApiException.network().message,
        );
      default:
        return ApiResponse.error(
          code: ApiException.network().code,
          message: ApiException.network().message,
        );
    }
  }

  ApiResponse _handleResponseError(Response? response) {
    if (response == null) {
      return ApiResponse.error(
        code: ApiException.unknown().code,
        message: ApiException.unknown().message,
      );
    }

    switch (response.statusCode) {
      case 401:
        return ApiResponse.error(
          code: ApiException.unauthorized().code,
          message: ApiException.unauthorized().message,
        );
      case 403:
        return ApiResponse.error(
          code: ApiException.forbidden().code,
          message: ApiException.forbidden().message,
        );
      case 404:
        return ApiResponse.error(
          code: ApiException.notFound().code,
          message: ApiException.notFound().message,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return ApiResponse.error(
          code: ApiException.server().code,
          message: ApiException.server().message,
        );
      default:
        return ApiResponse.error(
          code: response.statusCode.toString(),
          message: response.statusMessage ?? '未知错误',
        );
    }
  }

  /// 处理异常
  ApiException _handleException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException.timeout();
      case DioExceptionType.badResponse:
        if (e.response?.statusCode == 401) {
          return ApiException.unauthorized();
        } else if (e.response?.statusCode == 403) {
          return ApiException.forbidden();
        } else if (e.response?.statusCode == 404) {
          return ApiException.notFound();
        } else if (e.response?.statusCode != null &&
            e.response!.statusCode! >= 500) {
          return ApiException.server();
        }
        return ApiException(
          message: e.response?.statusMessage ?? '未知错误',
          code: e.response?.statusCode?.toString() ?? 'ERROR',
        );
      case DioExceptionType.cancel:
        return ApiException(message: '请求已取消', code: 'CANCELLED');
      default:
        return ApiException.network();
    }
  }

  /// 设置代理地址（为空则关闭代理）
  void setProxyAddress(String? address) {
    if (address != null && address.isNotEmpty) {
      dio.httpClientAdapter =
          IOHttpClientAdapter()
            ..onHttpClientCreate = (client) {
              client.findProxy = (uri) => 'PROXY $address';
              client.badCertificateCallback = (cert, host, port) => true;
              return client;
            };
    } else {
      dio.httpClientAdapter = IOHttpClientAdapter();
    }
  }
}
