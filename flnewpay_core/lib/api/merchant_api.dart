import 'dart:convert';
import 'dart:io';

import 'package:flnewpay_core/api/models/merchant/merchant_account_info_result.dart';

import 'package:flnewpay_core/api/models/merchant/merchant_confirm_order_req.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_create_cash_order.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_label.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_order_list_merchant_shop_order_req.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_order_refund_req.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_order_req.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_shop_item_result.dart';
import 'package:flnewpay_core/api/models/merchant/query_shop_info_time_req.dart';
import 'package:flnewpay_core/api/models/merchant/transfer_account_create_order_req.dart';
import 'package:flnewpay_core/api/models/merchant/update_req.dart';
import 'package:flnewpay_core/api/models/merchant/shop_business_req.dart';
import 'package:flnewpay_core/common/extensions/md5_str.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantexports.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrefund/orderrecordresult.dart';
import 'package:get/get.dart';

import 'package:flnewpay_core/common/utils/sign_util.dart';
import 'package:flnewpay_core/api/models/merchant/create_collect_money_order_req.dart';
import 'package:flnewpay_core/api/models/newpaydefine.dart';
import 'package:dio/dio.dart' as dio;

import 'models/merchant/merchant_add_bank_edit_info.dart';

extension MerchantApiPath on MerchantApi {
  /// 账号相关API
  static const String userLogin = 'api/merchant/app/login'; //done
  static const String userAddAccount = 'api/merchant/app/addAccount'; //done
  static const String userUpdateAccountInfo =
      'api/merchant/app/updateAccount'; //done
  static const String userDeleteAccount = 'api/merchant/app/delAccount'; //done
  static const String userStopAccount =
      'api/merchant/app/delAccountStatus'; //done冻结账号
  static const String userQueryAuth = 'api/merchant/app/queryPermission'; //done
  static const String userLogout = 'api/merchant/app/logout'; //done
  static const String userUpdatePassword =
      'api/merchant/app/updatePassword'; //done
  static const String userQueryAccountList =
      'api/merchant/app/queryAccountList'; //done
  static const String userQueryPhone = 'api/merchant/phone/'; //查询客户电话
  static const String userUploadIdCard = 'api/merchant/webapp/file/doulp/id';

  /// 商户收款相关API
  static const String shopCreateScanBill =
      'api/merchant/collect/createCollectMoneyOrder';
  static const String shopSureGetMoneyQuery =
      'api/merchant/collect/merchantConfirmOrder';
  // 复合支付
  static const String shopCreateScanBillNewApi = 'beta/pay/micropay';

  /// 营业数据相关API
  // static const String sellDataPage = 'api/merchant/data/pageShopData';
  // static const String sellDataQuery = 'api/merchant/data/queryShopData';
  static const String sellQuery = 'api/merchant/data/queryData';
  // static const String sellQueryPageOrder = 'api/merchant/data/pageOrder';

  /// 商户账单相关API
  static const String shopBillListQuery =
      'api/merchant/order/listMerchantShopOrder';
  static const String shopBillDetailQuery =
      'api/merchant/order/detailMerchantShopOrder';
  static const String shopManageInfoQuery =
      'api/merchant/shop/merchantShopInfo';

  /// 商户退款相关API
  static const String backMoneyBillCreate =
      'api/merchant/refund/createOrderRefund';
  static const String backMoneySure = 'api/merchant/refund/orderRefund';
  static const String backMoneyCancel = 'api/merchant/refund/orderRefundCancel';

  /// 结算相关API
  static const String querySettlementList = 'api/merchant/settlement';
  static const String querySettlementDetail = 'api/merchant/settlement/detail';

  /// 商户门店信息相关API
  static const String queryMerchantList = 'api/merchant/shop/listMerchantShop';
  static const String queryMerchantDetail =
      'api/merchant/shop/merchantShopBusinessData';

  /// 商户转账相关API
  static const String createBusinessTransferOrder =
      'api/merchant/transfer/createBusinessTransferOrder';
  static const String payBusinessTransferOrder =
      'api/merchant/transfer/payBusinessTransferOrder';
  static const String queryMerchantAcc =
      'api/merchant/transfer/queryMerchantAcc';
  static const String queryMerchantAccAndSettle =
      'api/merchant/transfer/queryMerchantAccAndSettle';
  static const String pageTradeRecord =
      'api/merchant/tradeRecord/pageTradeRecord';
  static const String queryPayUsdDetail =
      'api/merchant/tradeRecord/queryTradeRecordById';
  static const String queryBusinessById =
      'api/merchant/transfer/queryBusinessById';
  static const String queryCurrencyById = 'api/merchant/order/payCurrency';

  /// 验证码相关API
  static const String getSmsCode = 'api/merchant/smscode';
  static const String smsCodeCheck = 'api/merchant/smscode/check';
  static const String getPicCode = 'api/merchant/picCode';
  static const String picCodeCheck = 'api/merchant/picCode/check';

  /// 其他API
  static const String versionQuery = 'api/merchantapp/version/query';
  static const String walletInfo = 'api/merchant/wallet/getWalletInfo';
  static const String queryCityList = '/api/merchant/shop/queryCityTree';
  static const String queryRevUserInfoById =
      'api/merchant/transfer/queryRevUserInfo';

  /// 门店管理相关API
  static const String fetchStoreList = 'api/merchant/shop/pageShopInfo';
  static const String obtainStoreList = 'api/merchant/shop/selectOneShopInfo';
  static const String postFreezeThawStore = 'api/merchant/shop/uptShopStatus';
  static const String postDeleteStore = 'api/merchant/shop/delShopInfo';
  static const String postCreateStore = 'api/merchant/shop/addShopInfo';
  static const String postReeditStore = 'api/merchant/shop/uptShopInfo';
  static const String fetchProvinceCityList = 'api/merchant/shop/queryCityTree';

  /// 商户拉新相关API
  static const String queryMerchantInviteInfo =
      'api/merchant/invite/users/qrCodeAndTotalRewards';
  static const String queryMerchantInviteRecords =
      'api/merchant/invite/users/pageRecords';

  /// 商户标签相关API
  static const String fetchMerchantLabels = 'api/merchant/shop/labelList';

  /// 地图相关API
  static const String mapTextSearch = 'api/merchant/app/map/textsearch';
  static const String mapNearbySearch = 'api/merchant/app/map/nearbysearch';

  /// 收款开关相关API
  static const String postVoiceSwitch = 'api/merchant/push/voice/switch';
  static const String getVoiceSwitch = 'api/merchant/push/voice/switch';
  static const String getQrCode = 'api/merchant/qrcode/collectMoney';

  /// 银行卡相关
  static const String queryBankByUserId = 'beta/wd/page';
  static const String listBank = 'beta/wd/avlBankPage';
  static const String listAvailableWithdrawCurrency = 'beta/wd/currency';

  /// /{id}
  static const String queryBankCardDetail = 'beta/wd/accountDetail';
  static const String bindBankCard = 'beta/wd/save';

  /// /{id}
  static const String unbindBankCard = 'beta/wd/deleted';
  static const String createCashOrder = 'beta/wd/createWithdrawal';
}

/// 商户 API 服务
class MerchantApi extends GetxService {
  final ApiConfig _apiConfig = ApiConfig();
  final Http _http = Http();
}

extension MerchantManagerApi on MerchantApi {
  //版本检测
  Future<ApiResponse> versionQuery() async {
    int platform = Platform.isIOS ? 2 : 3;
    platform = Platform.isAndroid ? 1 : platform;
    final (version, code) = await PlatformUtils.getAppInfoVersionAndCode();
    final req = {"appType": 2, "platform": platform, "versionCode": code};
    final response = await _http.post(
      MerchantApiPath.versionQuery,
      data: jsonEncode({...req, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 商户登录
  Future<ApiResponse> login(LoginReq loginReq) async {
    final response = await _http.post(
      MerchantApiPath.userLogin,
      data: jsonEncode({...loginReq.toJson(), ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 更改密码
  Future<ApiResponse> updatePassword(
    String oldPassword,
    String newPassword,
    String mid,
  ) async {
    final updateReq = UpdateReq(
      oldPassword: oldPassword.getMd5Val(),
      newPassword: newPassword.getMd5Val(),
      id: mid,
    );
    final response = await _http.post(
      MerchantApiPath.userUpdatePassword,
      data: jsonEncode({...updateReq.toJson(), ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询用户权限
  Future<ApiResponse> queryPermission() async {
    final response = await _http.post(
      MerchantApiPath.userQueryAuth,
      data: _apiConfig.baseParameters,
    );
    return response;
  }

  /// 查询商户门店列表
  Future<ApiResponse> queryStoreMerchantList() async {
    final response = await _http.get(
      MerchantApiPath.queryMerchantList,
      parameters: _apiConfig.baseParameters,
      // fullUrl: 'http://************:8084/',
    );
    return response;
  }

  /// 查询商户门店业务数据
  Future<ApiResponse> merchantShopBusinessData({
    required String currency,
    required String shopId,
  }) async {
    final req = ShopBusinessReq(currency: currency, shopId: shopId);

    final response = await _http.post(
      MerchantApiPath.queryMerchantDetail,
      data: jsonEncode({...req.toJson(), ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 退出登录
  Future<ApiResponse> logout() async {
    final response = await _http.post(MerchantApiPath.userLogout);
    return response;
  }

  /// 查询门店营业数据
  Future<ApiResponse> queryShopInfoByTime({
    required int startTime,
    required int endTime,
    required String currency,
    String? shopName,
    String? shopId,
    String? businessId,
  }) async {
    final req = QueryShopInfoByTimeReq(
      startTime: startTime,
      endTime: endTime,
      currency: currency,
    );

    // 处理shopId逻辑
    if (shopName == null) {
      // 如果shopName为空，使用当前用户的shopId
      req.shopId = MerchantUser.instance.shopId;
    } else if (shopName == 'all'.tr) {
      // 如果选择全部门店
      req.shopId = '';
      req.businessId = MerchantUser.instance.businessId;
    } else if (shopId != null && shopId.isNotEmpty) {
      // 如果指定了shopId
      req.shopId = shopId;
    } else {
      // 默认使用当前用户的shopId
      req.shopId = MerchantUser.instance.shopId;
    }
    final response = await _http.post(
      MerchantApiPath.sellQuery,
      data: jsonEncode({...req.toJson(), ..._apiConfig.baseParameters}),
    );

    return response;
  }

  // 查询商户门店结算列表
  Future<ApiResponse> queryMerchantSettleData({
    required String pageNum,
    required String pageSize,
    required String shopId,
    required int startTime,
    required int endTime,
  }) async {
    final req = {
      "pageNum": pageNum,
      "pageSize": pageSize,
      "shopId": shopId,
      "startTime": startTime,
      "endTime": endTime,
    };
    final response = await _http.get(
      MerchantApiPath.querySettlementList,
      parameters: req,
    );
    return response;
  }

  // 查询商户门店结算详情
  Future<ApiResponse> queryMerchantSettleDetail({
    required int pageNum,
    required int pageSize,
    required String settleId,
    required String shopId,
    required String payMethod,
  }) async {
    final req = {
      'settlementId': settleId,
      'pageNum': pageNum,
      'pageSize': pageSize,
      'shopId': shopId,
    };
    if (payMethod.isNotEmpty) {
      req['payMethod'] = payMethod;
    }
    final response = await _http.get(
      MerchantApiPath.querySettlementDetail,
      parameters: req,
    );
    return response;
  }

  // shopCreateScanBill
  Future<ApiResponse> shopCreateScanBill({
    required String currency,
    required String shopId,
    required String orderAmt,
    required String userCodeValue,
    required String payMethod,
  }) async {
    final req = CreateCollectMoneyOrderReq(
      orderAmt: orderAmt,
      payMethod: payMethod,
      userCodeValue: userCodeValue,
      shopId: shopId,
      currency: currency,
    );

    // 生成签名
    final signMap = {
      "orderAmt": orderAmt,
      "payMethod": payMethod,
      "userCodeValue": userCodeValue,
      "shopId": shopId,
      "currency": currency,
    };

    req.sign = SignUtil.sign(signMap);
    final response = await _http.post(
      MerchantApiPath.shopCreateScanBill,
      data: jsonEncode({...req.toJson(), ..._apiConfig.baseParameters}),
    );

    return response;
  }

  //新版创建扫码订单
  Future<ApiResponse> shopCreateScanOrderNewApi({
    required String currency,
    required String shopId,
    required String orderAmt,
    required String userCodeValue,
    required String payMethod,
    String? customUrl,
  }) async {
    // 生成签名
    final signMap = {
      "amount": orderAmt,
      "authCode": userCodeValue,
      "shopId": shopId,
      "payMethod": payMethod,
      "currency": currency,
    };
    signMap['sign'] = SignUtil.sign(signMap) ?? '';
    final response = await _http.post(
      MerchantApiPath.shopCreateScanBillNewApi,
      data: jsonEncode({...signMap, ..._apiConfig.baseParameters}),
      fullUrl: customUrl,
    );

    return response;
  }

  // 确认订单是否支付
  Future<ApiResponse> recieveCashPaysatusQuery(String orderNo) async {
    Map<String, String> signMap = {"orderNo": orderNo};
    final sign = SignUtil.sign(signMap);
    final req = {"orderNo": orderNo, "sign": sign};
    final response = await _http.post(
      MerchantApiPath.shopSureGetMoneyQuery,
      data: jsonEncode({...req, ..._apiConfig.baseParameters}),
      showLoading: false,
    );
    return response;
  }

  // 查询账户列表
  Future<List<AccountItemInfo>> userQueryAccountList(
    int page,
    int pageSize,
    String shopId,
  ) async {
    final req = {
      "pageSize": pageSize.toString(),
      "pageNum": page.toString(),
      "shopId": shopId,
    };
    final response = await _http.post(
      MerchantApiPath.userQueryAccountList,
      data: jsonEncode(req),
    );
    if (response.isSuccess) {
      final data = response.data as Map<String, dynamic>?;
      final records = data?['records'] as List<dynamic>?;
      return records?.map((e) => AccountItemInfo.fromJson(e)).toList() ?? [];
    }
    return [];
  }

  // shopSureGetMoneyQuery
  Future<ApiResponse> shopSureGetMoneyQuery(String orderId) async {
    //orderNo
    final signMap = {"orderNo": orderId};
    final req = MerchantConfirmOrderReq(
      orderNo: orderId,
      sign: SignUtil.sign(signMap) ?? '',
    );
    final response = await _http.post(
      MerchantApiPath.shopSureGetMoneyQuery,
      data: req.toJsonString(),
    );
    return response;
  }

  //shopBillListQuery
  Future<(List<OrderRefundRecord>?, int)> shopBillListQuery(
    int page,
    int pageSize,
    String shopId,
    NewPayMethod payMethod,
    int startTime,
    int endTime,
    OrderRefundSelectType selectType, //订单管理退款管理 1 2
  ) async {
    final req = MerchantOrderListMerchantShopOrderReq(
      shopId: shopId,
      payMethod: payMethod,
      startTime: startTime.toString(),
      endTime: endTime.toString(),
      selectType: selectType,
      pageNum: page.toString(),
      pageSize: pageSize.toString(),
    );
    final response = await _http.post(
      MerchantApiPath.shopBillListQuery,
      data: req.toJsonString(),
    );
    if (response.isSuccess) {
      final data = response.data as Map<String, dynamic>?;
      final records = data?['records'] as List<dynamic>?;
      final total = data?['total'] as int?;
      return (
        records?.map((e) => OrderRefundRecord.fromJson(e)).toList() ?? [],
        total ?? 0,
      );
    }
    return (null, 0);
  }

  Future<ApiResponse> shopBillDetailQuery(String orderNo) async {
    final signMap = {"orderNo": orderNo};
    final req = MerchantOrderDetailMerchantShopOrderReq(
      orderNo: orderNo,
      sign: SignUtil.sign(signMap) ?? '',
    );
    final response = await _http.post(
      MerchantApiPath.shopBillDetailQuery,
      data: req.toJsonString(),
    );
    return response;
  }

  Future<ApiResponse> backMoneyBillCreate(
    String orderNo,
    String refundAmount,
  ) async {
    final signMap = {"refundOrderAmt": refundAmount, "payOrderId": orderNo};
    final req = MerchantOrderRefundReq(
      refundOrderAmt: refundAmount,
      payOrderId: orderNo,
      sign: SignUtil.sign(signMap) ?? '',
    );

    final response = await _http.post(
      MerchantApiPath.backMoneyBillCreate,
      data: req.toJsonString(),
    );
    return response;
  }

  Future<ApiResponse> backMoneySure(String refundOrderNo) async {
    final signMap = {"refundOrderNo": refundOrderNo};
    final req = {
      "refundOrderNo": refundOrderNo,
      "sign": SignUtil.sign(signMap),
    };
    final response = await _http.post(
      MerchantApiPath.backMoneySure,
      data: jsonEncode(req),
    );
    return response;
  }

  Future<ApiResponse> backMoneyCancel(String refundOrderNo) async {
    final signMap = {"refundOrderNo": refundOrderNo};
    final req = {
      "refundOrderNo": refundOrderNo,
      "sign": SignUtil.sign(signMap),
    };
    final response = await _http.post(
      MerchantApiPath.backMoneyCancel,
      data: jsonEncode(req),
    );
    return response;
  }

  /// 查询商户门店业务数据
  Future<ApiResponse> shopManageInfoQuery({required String shopId}) async {
    final req = {"shopId": shopId};
    final response = await _http.post(
      MerchantApiPath.shopManageInfoQuery,
      data: jsonEncode({...req, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  //商店二维码
  Future<ApiResponse> queryMerchantShopQrCode() async {
    final response = await _http.get(
      MerchantApiPath.getQrCode,
      parameters: _apiConfig.baseParameters,
    );
    return response;
  }

  //添加店员
  Future<ApiResponse> merchantAccountManagerAdd(
    Map<String, dynamic> data,
  ) async {
    final response = await _http.post(
      MerchantApiPath.userAddAccount,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 更新账户状态
  Future<ApiResponse> merchantAccountManagerStopAccount(
    String accountId,
    String status,
    String accountName,
  ) async {
    // {"accountName":"djekjdj","accountStatus":"1","id":"1912776517874106370"}
    final req = {
      'id': accountId,
      'accountStatus': status,
      'accountName': accountName,
    };
    final response = await _http.post(
      MerchantApiPath.userStopAccount,
      data: jsonEncode({...req, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 更新账户信息
  Future<ApiResponse> merchantAccountManagerUpdate(
    Map<String, dynamic> params,
  ) async {
    final response = await _http.post(
      MerchantApiPath.userUpdateAccountInfo,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 删除账户
  Future<ApiResponse> merchantAccountManagerDeleteAccount(
    String accountId,
    String accountName,
  ) async {
    final response = await _http.post(
      MerchantApiPath.userDeleteAccount,
      data: jsonEncode({'id': accountId, 'accountName': accountName}),
    );
    return response;
  }

  //门店相关查询
  Future<List<MerchantShopItemResult>> userQueryShopList(
    int page,
    int pageSize,
    String businessId,
  ) async {
    final req = {
      "pageSize": pageSize.toString(),
      "pageNum": page.toString(),
      "businessId": businessId,
    };
    final response = await _http.post(
      MerchantApiPath.fetchStoreList,
      data: jsonEncode(req),
    );
    if (response.isSuccess) {
      final data = response.data as Map<String, dynamic>?;
      final records = data?['records'] as List<dynamic>?;
      return records?.map((e) => MerchantShopItemResult.fromJson(e)).toList() ??
          [];
    }
    return [];
  }

  //门店相关添加
  Future<ApiResponse> merchantShopAdd(Map<String, dynamic> data) async {
    final response = await _http.post(
      MerchantApiPath.postCreateStore,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  //门店相关修改
  Future<ApiResponse> merchantShopUpdate(Map<String, dynamic> data) async {
    final response = await _http.post(
      MerchantApiPath.postReeditStore,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  //门店相关删除
  Future<ApiResponse> merchantShopDelete(String shopId) async {
    final response = await _http.post(
      MerchantApiPath.postDeleteStore,
      data: jsonEncode({'id': shopId}),
    );
    return response;
  }

  //门店相关冻结解冻
  Future<ApiResponse> merchantShopFreezeThaw(
    String storeId,
    String status,
  ) async {
    final response = await _http.post(
      MerchantApiPath.postFreezeThawStore,
      data: jsonEncode({'id': storeId, 'shopStatus': status}),
    );
    return response;
  }

  // 上传身份证图片
  Future<ApiResponse> uploadImage(String path) async {
    try {
      final file = File(path);

      // 使用 FormData
      final formData = dio.FormData.fromMap({
        'file': await dio.MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
      });

      // 发送请求
      final response = await _http.post(
        MerchantApiPath.userUploadIdCard,
        data: formData,
      );
      return response;
    } catch (e) {
      return ApiResponse.error(message: e.toString(), code: 'ERROR');
    }
  }

  Future<ApiResponse> queryCityList() async {
    final response = await _http.get(
      MerchantApiPath.queryCityList,
      parameters: null,
      showLoading: false,
    );
    return response;
  }

  Future<ApiResponse> obtainStoreList(String merchantShopItemId) async {
    final response = await _http.post(
      MerchantApiPath.obtainStoreList,
      data: jsonEncode({'id': merchantShopItemId}),
    );
    return response;
  }

  Future<List<MerchantChooseLabel>> fetchMerchantLabelList() async {
    final response = await _http.post(
      MerchantApiPath.fetchMerchantLabels,
      parameters: _apiConfig.baseParameters,
    );
    if (response.isSuccess) {
      final data = response.data as Map<String, dynamic>?;
      final labels = data?['labelList'] as List<dynamic>?;
      return labels?.map((e) => MerchantChooseLabel.fromJson(e)).toList() ?? [];
    }
    return [];
  }

  // 查询客户电话
  Future<String> queryCustomerPhone() async {
    final response = await _http.get(MerchantApiPath.userQueryPhone);
    if (response.isSuccess) {
      final data = response.data as Map<String, dynamic>?;
      final phone = data?['mobilePhone'] as String?;
      return phone ?? '';
    }
    return '';
  }
}

//转账相关
extension MerchantTransferApi on MerchantApi {
  /// 创建商户转账订单
  /// @param params 转账参数，包含以下字段：
  /// - amount: 转账金额
  /// - currency: 货币类型
  /// - toBusinessId: 目标商户ID
  /// - remark: 备注信息
  Future<ApiResponse> createBusinessTransferOrder(
    TransferAccountReq params,
  ) async {
    Map<String, String> signmap = {
      'fundAccountNumber': params.fundAccountNumber ?? '',
      'orderAmt': params.orderAmt ?? '',
      'currency': params.currency ?? '',
      'orderDesc': params.orderDesc ?? '',
    };
    signmap['sign'] = SignUtil.sign(signmap) ?? '';
    final response = await _http.post(
      MerchantApiPath.createBusinessTransferOrder,
      data: jsonEncode({...signmap, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 支付转账订单
  Future<ApiResponse> createpayBusinessTransferOrder({
    required String orderNo,
    required String validToken,
  }) async {
    Map<String, String> signmap = {
      'orderNo': orderNo,
      'validToken': validToken,
    };

    signmap['sign'] = SignUtil.sign(signmap) ?? '';
    final response = await _http.post(
      MerchantApiPath.payBusinessTransferOrder,
      data: jsonEncode({...signmap, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询商户转账订单
  /// @param params 查询参数，包含以下字段：
  /// - orderId: 订单ID
  Future<ApiResponse> queryRevUserInfoById(String fundAccountNumber) async {
    final response = await _http.get(
      MerchantApiPath.queryRevUserInfoById,
      parameters: {'fundAccountNumber': fundAccountNumber},
    );
    return response;
  }

  /// - payPassword: 支付密码
  Future<ApiResponse> payBusinessTransferOrder(
    Map<String, dynamic> params,
  ) async {
    final response = await _http.post(
      MerchantApiPath.payBusinessTransferOrder,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询商户账户信息
  /// @param params 查询参数，包含以下字段：
  /// - businessId: 商户ID
  /// - currency: 货币类型
  Future<ApiResponse> queryMerchantAcc(Map<String, dynamic> params) async {
    final response = await _http.post(
      MerchantApiPath.queryMerchantAcc,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询商户账户和结算信息
  /// @param params 查询参数，包含以下字段：
  /// - businessId: 商户ID
  /// - currency: 货币类型
  Future<ApiResponse> queryMerchantAccAndSettle(
    Map<String, dynamic> params,
  ) async {
    final response = await _http.post(
      MerchantApiPath.queryMerchantAccAndSettle,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 分页查询商户交易记录
  /// @param params 查询参数，包含以下字段：
  /// - pageNum: 页码
  /// - pageSize: 每页数量
  /// - businessId: 商户ID
  /// - currency: 货币类型
  /// - startTime: 开始时间
  /// - endTime: 结束时间
  Future<ApiResponse> getPageTradeRecord(int pageNum, int pageSize) async {
    final response = await _http.post(
      MerchantApiPath.pageTradeRecord,
      data: jsonEncode({
        'pageNum': pageNum,
        'pageSize': pageSize,
        ..._apiConfig.baseParameters,
      }),
    );
    return response;
  }

  /// 查询交易记录详情
  /// @param params 查询参数，包含以下字段：
  /// - tradeId: 交易ID
  Future<ApiResponse> queryPayUsdDetail(String recordId) async {
    final response = await _http.get(
      MerchantApiPath.queryPayUsdDetail,
      parameters: {'id': recordId},
    );
    return response;
  }

  /// 根据ID查询商户信息
  /// @param params 查询参数，包含以下字段：
  /// - businessId: 商户ID
  Future<ApiResponse> queryBusinessById(Map<String, dynamic> params) async {
    final response = await _http.post(
      MerchantApiPath.queryBusinessById,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 根据ID查询货币信息
  /// @param params 查询参数，包含以下字段：
  /// - currencyId: 货币ID
  Future<ApiResponse> queryCurrencyById(Map<String, dynamic> params) async {
    final response = await _http.post(
      MerchantApiPath.queryCurrencyById,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );
    return response;
  }
}

//钱包相关API

extension MerchantWalletApi on MerchantApi {
  /// 查询商户钱包信息
  /// @param businessId 商户ID
  Future<ApiResponse> queryWalletInfo(String businessId) async {
    Map<String, dynamic> params = {'businessId': businessId};
    final response = await _http.post(
      MerchantApiPath.walletInfo,
      data: jsonEncode({...params, ..._apiConfig.baseParameters}),
    );

    return response;
  }

  /// 查询银行卡开卡户机构列表
  Future<ApiResponse> listBank({
    required int pageNum,
    required int pageSize,
    required String currency,
  }) async {
    final Map<String, dynamic> data = {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'currency': currency,
    };
    final response = await _http.post(
      MerchantApiPath.listBank,
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 获取银行卡绑卡信息
  ///
  /// pageNum 页码
  ///
  /// pageSize 页大小
  ///
  /// currency 货币类型
  Future<ApiResponse> listBoundBank({
    required int pageNum,
    required int pageSize,
    required String currency,
  }) async {
    final Map<String, dynamic> data = {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'currency': currency,
    };
    final response = await _http.post(
      MerchantApiPath.queryBankByUserId,
      // fullUrl: ApiConfig.newApiUrl,
      // fullUrl: 'http://************:18081/',
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询提现币种
  Future<ApiResponse> listWithdrawCurrency() async {
    final response = await _http.get(
      MerchantApiPath.listAvailableWithdrawCurrency,
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 查询银行卡详情
  ///
  /// id 银行卡id
  Future<ApiResponse> queryBankCardDetail({required String id}) async {
    final response = await _http.get(
      '${MerchantApiPath.queryBankCardDetail}/$id',
      // fullUrl: ApiConfig.newApiUrl,
      // fullUrl: 'http://************:18081/',
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 绑定银行卡
  ///
  /// data 银行卡信息
  Future<ApiResponse> bindBankCard({required MerchantBankEditInfo data}) async {
    final response = await _http.post(
      MerchantApiPath.bindBankCard,
      // fullUrl: ApiConfig.newApiUrl,
      // fullUrl: 'http://************:18081/',
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({...data.toJson(), ..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 解绑银行卡
  ///
  /// id 银行卡id
  Future<ApiResponse> unbindBankCard({required String id}) async {
    final response = await _http.delete(
      '${MerchantApiPath.unbindBankCard}/$id',
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({..._apiConfig.baseParameters}),
    );
    return response;
  }

  /// 发起提现
  ///
  /// data 提现信息
  Future<ApiResponse> createCashOrder({
    required MerchantCreateCashOrder data,
  }) async {
    final response = await _http.post(
      MerchantApiPath.createCashOrder,
      // fullUrl: ApiConfig.newApiUrl,
      // fullUrl: 'http://************:18081/',
      fullUrl: ApiConfig().getBaseUrl,
      data: jsonEncode({...data.toJson(), ..._apiConfig.baseParameters}),
    );
    return response;
  }
}

// SMS verification related API methods
extension MerchantSmsVerificationApi on MerchantApi {
  // 获取短信验证码
  Future<ApiResponse> getSmsCode({
    required int type,
    String? picToken,
    String? customUrl,
  }) async {
    final Map<String, dynamic> data = {
      'type': type,
      if (picToken != null) 'picToken': picToken,
    };
    final response = await _http.post(
      MerchantApiPath.getSmsCode,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
      fullUrl: customUrl,
    );
    return response;
  }

  // 校验短信验证码
  Future<ApiResponse> checkSmsCode({
    required String smsCode,
    required int type,
  }) async {
    final Map<String, dynamic> data = {'smsCode': smsCode, 'type': type};

    final response = await _http.post(
      MerchantApiPath.smsCodeCheck,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 校验图形验证码
  Future<ApiResponse> checkPicCode({
    required String picToken,
    required String picCode,
  }) async {
    final Map<String, dynamic> data = {
      'picToken': picToken,
      'picCode': picCode,
    };

    final response = await _http.post(
      MerchantApiPath.picCodeCheck,
      data: jsonEncode({...data, ..._apiConfig.baseParameters}),
    );
    return response;
  }

  // 获取图形验证码
  Future<ApiResponse> getQueryPicCode() async {
    final response = await _http.get(MerchantApiPath.getPicCode);
    return response;
  }
}
