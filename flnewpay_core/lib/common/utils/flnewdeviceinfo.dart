import 'dart:io' show Platform;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart' show kIsWeb, debugPrint;
import 'package:flnewpay_core/common/utils/log.dart';

extension PackageInfoExtension on PackageInfo {
  String toJson() {
    return '''
    {
      "appName": "$appName",
      "packageName": "$packageName",
      "version": "$version",
      "buildNumber": "$buildNumber",
      "buildSignature": "$buildSignature",
      "installerStore": "$installerStore"
    }
  ''';
  }
}

class DeviceInfoService {
  DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  Future<DeviceInfoObj> buildDeviceInfo() async {
    DeviceInfoObj obj = DeviceInfoObj();

    try {
      if (kIsWeb) {
        // Web 平台处理
        WebBrowserInfo webInfo = await deviceInfoPlugin.webBrowserInfo;
        obj.model = webInfo.browserName.name;
        obj.vendor = webInfo.vendor ?? 'unknown';
        obj.uuid = await _getUUID();
      } else if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        obj.model = "${androidInfo.manufacturer}-${androidInfo.model}";
        obj.vendor = androidInfo.manufacturer;
        obj.uuid = await _getAndroidId();
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        obj.model = "${iosInfo.name}-${iosInfo.model}";
        obj.vendor = "Apple";
        obj.uuid = await _getUUID();
      } else {
        obj.model = "unknown";
        obj.vendor = "unknown";
        obj.uuid = await _getUUID();
      }
    } catch (e) {
      Log.v('获取设备信息错误: $e');
      // 出错时使用默认值
      obj.model = "unknown";
      obj.vendor = "unknown";
      obj.uuid = await _getUUID();
    }

    return obj;
  }

  Future<String> _getAndroidId() async {
    if (!kIsWeb && Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
      return androidInfo.id;
    }
    return await _getUUID();
  }

  Future<String> _getUUID() async {
    return const Uuid().v4();
  }
}

class DeviceInfoObj {
  String? model;
  String? vendor;
  String? uuid;
  String? packageInfo;

  DeviceInfoObj({this.model, this.vendor, this.uuid, this.packageInfo});

  Map<String, dynamic> toJson() => {
    'model': model,
    'vendor': vendor,
    'uuid': uuid,
    'packageInfo': packageInfo,
  };

  factory DeviceInfoObj.fromJson(Map<String, dynamic> json) => DeviceInfoObj(
    model: json['model'],
    vendor: json['vendor'],
    uuid: json['uuid'],
    packageInfo: json['packageInfo'],
  );
}
