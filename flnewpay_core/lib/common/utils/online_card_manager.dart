import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flnewpay_core/api/models/merchant/merchant_user.dart';

/// 卡号管理器
class OnlineCardManager {
  static const String _storageKey = 'RECENT_TRANSFER_CARDS';
  static const int _maxCards = 5;

  /// 添加最近使用的卡号
  static Future<void> addRecentCard(
    String cardNumber,
    String accountName,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = MerchantUser.instance.id;
      final key = '${_storageKey}_$userId';

      // 获取现有记录
      final existingData = prefs.getString(key);
      List<Map<String, dynamic>> cards = [];

      if (existingData != null) {
        cards = List<Map<String, dynamic>>.from(jsonDecode(existingData));
      }

      // 检查是否已存在
      final existingIndex = cards.indexWhere(
        (card) => card['cardNumber'] == cardNumber,
      );
      if (existingIndex != -1) {
        // 如果已存在，移除旧记录
        cards.removeAt(existingIndex);
      }

      // 添加新记录到开头
      cards.insert(0, {
        'cardNumber': cardNumber,
        'accountName': accountName,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      // 保持最大数量限制
      if (cards.length > _maxCards) {
        cards = cards.sublist(0, _maxCards);
      }

      // 保存到本地存储
      await prefs.setString(key, jsonEncode(cards));
    } catch (e) {
      print('保存最近使用卡号失败: $e');
    }
  }

  /// 获取最近使用的卡号列表
  static Future<List<Map<String, dynamic>>> getRecentCards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = MerchantUser.instance.id;
      final key = '${_storageKey}_$userId';

      final existingData = prefs.getString(key);
      if (existingData != null) {
        return List<Map<String, dynamic>>.from(jsonDecode(existingData));
      }
    } catch (e) {
      print('获取最近使用卡号失败: $e');
    }
    return [];
  }

  /// 删除指定的最近使用卡号
  static Future<void> deleteRecentCard(String cardNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = MerchantUser.instance.id;
      final key = '${_storageKey}_$userId';

      // 获取现有记录
      final existingData = prefs.getString(key);
      if (existingData != null) {
        final cards = List<Map<String, dynamic>>.from(jsonDecode(existingData));

        // 移除指定卡号
        cards.removeWhere((card) => card['cardNumber'] == cardNumber);

        // 保存更新后的列表
        await prefs.setString(key, jsonEncode(cards));
      }
    } catch (e) {
      print('删除最近使用卡号失败: $e');
    }
  }

  /// 清除最近使用的卡号列表
  static Future<void> clearRecentCards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = MerchantUser.instance.id;
      final key = '${_storageKey}_$userId';
      await prefs.remove(key);
    } catch (e) {
      print('清除最近使用卡号失败: $e');
    }
  }
}
