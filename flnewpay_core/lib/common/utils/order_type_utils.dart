import 'package:flnewpay_core/api/models/merchant/merchant_wallet_record_detail.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 订单类型工具类
class OrderTypeUtils {
  /// 获取订单类型名称
  static String getTypeName(int type) {
    switch (type) {
      case 1: // 商户提现
        return 'withdraw'.tr;
      case 2: // 商户转账-转入
        return 'transfer_in'.tr;
      case 3: // 商户结算
        return 'settle'.tr;
      case 4: // 商户转账-转出
        return 'transfer_out'.tr;
      case 5: // 商户发工资
        return 'salary'.tr;
      default:
        return 'unknown'.tr;
    }
  }

  static String getTipName(int type) {
    switch (type) {
      case 1: // 商户提现
        return '${'withdraw'.tr}${'arrive'.tr}';
      case 2: // 商户转账-转入
        return '${'transfer_in'.tr}${'from'.tr}';
      case 3: // 商户结算
        return '${'settle'.tr}${'from'.tr}';
      case 4: // 商户转账-转出
        return '${'transfer_out'.tr}${'arrive'.tr}';
      case 5: // 商户发工资
        return '${'salary'.tr}${'from'.tr}';
      default:
        return 'unknown'.tr;
    }
  }

  /// 获取订单图标
  static IconData getTypeIcon(int type) {
    switch (type) {
      case 1: // 商户提现
        return Icons.currency_exchange_rounded;
      case 2: // 商户转账-转入
        return Icons.sync_alt;
      case 3: // 商户结算
        return Icons.account_balance_wallet;
      case 4: // 商户转账-转出
        return Icons.sync_alt;
      case 5: // 商户发工资
        return Icons.monetization_on;
      default:
        return Icons.help;
    }
  }

  /// 获取订单金额颜色
  static Color getAmountColor(int type) {
    switch (type) {
      case 1: // 商户提现
        return Colors.red;
      case 2: // 商户转账-转入
        return Colors.green;
      case 3: // 商户结算
        return Colors.green;
      case 4: // 商户转账-转出
        return Colors.red;
      case 5: // 商户发工资
        return Colors.red;
      default:
        return Colors.black;
    }
  }

  /// 判断是否为增加金额
  static bool isAddAmount(int type) {
    switch (type) {
      case 1: // 商户提现
        return false;
      case 2: // 商户转账-转入
        return true;
      case 3: // 商户结算
        return true;
      case 4: // 商户转账-转出
        return false;
      case 5: // 商户发工资
        return false;
      default:
        return true;
    }
  }

  /// 获取订单转账状态名称
  ///
  /// NONE:无状态,PROCESSING:处理中,SUCCESS:成功,FAILURE:失败
  static String getWithdrawStateName(String? state) {
    switch (state) {
      case 'PROCESSING':
        return 'order_withdraw_state_processing'.tr;
      case 'SUCCESS':
        return 'order_withdraw_state_success'.tr;
      case 'FAILURE':
        return 'order_withdraw_state_fail'.tr;
      default:
        return '';
    }
  }

  /// 获取订单转账进度名称
  ///
  /// create :发起提现,loading: 处理中,success: 提现到账
  static String getWithdrawProgressName(String? state) {
    switch (state) {
      case 'create':
        return 'order_withdraw_state_detail_create'.tr;
      case 'loading':
        return 'order_withdraw_state_detail_processing'.tr;
      case 'success':
        return 'order_withdraw_state_detail_complete'.tr;
      default:
        return 'order_withdraw_state_fail'.tr;
    }
  }

  /// 获取订单转账状态颜色
  ///
  /// NONE:无状态,PROCESSING:处理中,SUCCESS:成功,FAILURE:失败
  static Color getWithdrawStateColor(String? state) {
    switch (state) {
      case 'PROCESSING':
        return Colors.orange;
      case 'SUCCESS':
        return Colors.green;
      case 'FAILURE':
        return Colors.red;
      default:
        return Colors.red;
    }
  }
}
