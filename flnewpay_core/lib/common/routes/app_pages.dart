import 'package:flnewpay_core/common/app_config.dart';
import 'package:flnewpay_core/pages/chooselocation/bindding.dart';
import 'package:flnewpay_core/pages/chooselocation/view.dart';
import 'package:flnewpay_core/pages/chooselocationserch/bindding.dart';
import 'package:flnewpay_core/pages/chooselocationserch/view.dart';
import 'package:flnewpay_core/pages/choosephonecountry/binding.dart';
import 'package:flnewpay_core/pages/choosephonecountry/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantapplogin/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbankcard/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbankcard/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbankcarddetail/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbankcarddetail/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbanklist/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbanklist/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbindbankcard/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantbindbankcard/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantenviromentchange/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantenviromentchange/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantmain/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderdetail/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderdetail/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrecievecash/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantaccountadd/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantaccountadd/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantaccountmanager/bindings.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantaccountmanager/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantapilog/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrecordbrief/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrecordbrief/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrecorddetail/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrecorddetail/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrefund/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantpayresult/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantpayresult/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantprofile/controller.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantprofile/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrechargewithdrawwebview/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrechargewithdrawwebview/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrecievecash/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrootloader/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsettledetail/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsettledetail/view.dart';

import 'package:flnewpay_core/pages/merchantApp/merchantshopadd/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantshopadd/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantshopmanager/bindings.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantshopmanager/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantstoreselectchange/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantstoreselectchange/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferconfirm/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferconfirm/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferfinalsmscode/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferfinalsmscode/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferidcheck/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferidcheck/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferrecordreceipt/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanttransferrecordreceipt/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwallet/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwebview/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwebview/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwithdrawbank/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwithdrawbank/view.dart';

import 'package:flnewpay_core/pages/newpayApp/login/view.dart';
import 'package:flnewpay_core/pages/proxypagesetting/binding.dart';
import 'package:flnewpay_core/pages/proxypagesetting/view.dart';
import 'package:get/get.dart';
import 'package:flnewpay_core/pages/newpayApp/container/binding.dart';
import 'package:flnewpay_core/pages/newpayApp/container/index.dart';
import 'package:flnewpay_core/pages/newpayApp/newpayfriends/index.dart';
import 'package:flnewpay_core/pages/newpayApp/newpayhome/index.dart';
import 'package:flnewpay_core/pages/newpayApp/profile/index.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantapplogin/index.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsetting/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsetting/controller.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantchangepassword/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantchangepassword/controller.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantrootloader/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchanthome/view.dart';

import 'package:flnewpay_core/pages/not_found/view.dart';
import 'package:flnewpay_core/pages/newpayApp/container/view.dart';
import 'package:flnewpay_core/pages/newpayApp/newpayhome/view.dart';
import 'package:flnewpay_core/pages/newpayApp/newpayfriends/view.dart';
import 'package:flnewpay_core/pages/newpayApp/profile/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantmain/index.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantscanqrcode/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantscanqrcode/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantgeneratecode/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantgeneratecode/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantinvite/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantwallet/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsettlemanager/view.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantsettlemanager/binding.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantinvite/controller.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantapilog/controller.dart';
import 'package:flnewpay_core/pages/merchantApp/merchantorderrefund/controller.dart';

/// 应用路由和页面配置
abstract class AppPages {
  // NewPay用户端路由
  static const container = '/container';
  static const login = '/login';
  static const home = '/home';
  static const friends = '/friends';
  static const profile = '/profile';
  static const visaApplication = '/visa_application';

  // 老挝APP路由
  static const laowoappcontainer = '/laowo/container';
  static const laowoapphome = '/laowo/home';
  static const laowofriends = '/laowo/friends';
  static const laowoprofile = '/laowo/profile';

  // 商户端路由
  static const merchantRoot = '/merchant';
  static const merchantMain = '/merchant/main';
  static const merchantLogin = '/merchant/login';
  static const merchantHome = '/merchant/home';
  static const merchantAnalytics = '/merchant/analytics';
  static const merchantProfile = '/merchant/profile';
  static const merchantSetting = '/merchant/merchantSetting';
  static const merchantChangePassword = '/merchant/changepassword';
  static const merchantWallet = '/merchant/wallet';
  static const merchantShopManager = '/merchant/shopManager';
  static const accountManagement = '/merchant/accountManagement';
  static const merchantInvite = '/merchant/invite';
  static const merchantQRCode = '/merchant/qrcode';
  static const merchantRecieveCash = '/merchant/recieve-cash';
  static const merchantOrder = '/merchant/order';
  static const merchantScan = '/merchant/scan';
  static const merchantCustomerService = '/merchant/customer-service';
  static const merchantWebview = '/merchant/webview';
  static const merchantApiLog = '/merchant/log-record';
  static const merchantOrderRefund = '/merchant/order-refund';
  // 订单详情
  static const merchantorderdetail = '/merchant/order-detail';

  static const merchantAccountManagerAdd = '/merchant/account-manager-add';
  // 添加账号
  static const merchantAccountManagerEditor =
      '/merchant/account-manager-editor';
  // 选择手机归属地
  static const choosePhoneCountry = '/merchant/choose-phone-country';
  // 门店添加
  static const merchantShopAdd = '/merchant/shop-add';
  // 选择位置
  static const chooseLocation = '/merchant/choose-location';
  // 选择位置搜索
  static const chooseLocationSearch = '/merchant/choose-location-search';
  // 钱包卡号检查
  static const merchantWalletIdCheck = '/merchant/wallet-id-check';
  // 转账确认
  static const merchantTransferConfirm = '/merchant/transfer-confirm';
  // 结算管理
  static const merchantSettleManager = '/merchant/settle-manager';
  static const merchantRechargeWithdrawWebView =
      '/merchant/recharge-withdraw-webview';
  // 订单记录简要信息
  static const merchantOrderRecordBrief = '/merchant/order-record-brief';
  // 订单记录详情
  static const merchantOrderRecordDetail = '/merchant/order-record-detail';
  // 短信验证码转账确认
  static const merchantTransferFinalSmscode =
      '/merchant/transfer-final-smscode';
  // 支付结果
  static const merchantPayResult = '/merchant/pay-result';
  //回执单详情转账用到
  static const merchantTransferRecordReceipt =
      '/merchant/transfer-record-receipt';
  // 通用路由
  static const notFound = '/not-found';

  // 结算详情
  static const merchantSettleDetail = '/merchant/settle-detail';

  // 门店选择
  static const storeSelect = '/merchant/store-select';

  // 代理设置
  static const proxyPageSetting = '/merchant/proxy-page-setting';

  // 环境切换
  static const merchantEnvironmentChange = '/merchant/environment-change';

  // 提现银行
  static const merchantWithdrawBank = '/merchant/withdraw-bank';

  /// 银行卡管理
  static const merchantBankCardManagement = '/merchant/bank-card-management';

  /// 选择开户行
  static const merchantBankList = '/merchant/bank-list';

  /// 绑定银行卡
  static const merchantBindBankCard = '/merchant/bind-bank-card';

  /// 银行卡详情
  static const merchantBankCardDetail = '/merchant/bank-card-detail';

  /// NewPay用户端页面
  static final List<GetPage> newPayPages = [
    GetPage(name: login, page: () => const NewPayAppLoginPage()),
    GetPage(
      name: container,
      page: () => const ContainerPage(),
      binding: ContainerBinding(),
    ),
    GetPage(name: home, page: () => const NewPayHomePage()),
    GetPage(name: friends, page: () => const NewPayFriendsPage()),
    GetPage(name: profile, page: () => const NewPayProfilePage()),
    GetPage(name: notFound, page: () => NotFoundPage()),
    GetPage(
      name: merchantWebview,
      page: () => const MerchantWebViewPage(),
      binding: MerchantWebViewBinding(),
    ),
  ];

  /// 商户端页面
  static final List<GetPage> merchantPages = [
    // 商户应用根路由 - 用于重启应用
    GetPage(
      name: merchantRoot,
      page: () => const MerchantRootLoaderPage(),
      binding: MerchantRootLoaderBinding(),
    ),
    // 登录页
    GetPage(
      name: merchantLogin,
      page: () => const NewPayMerchantAppLoginPage(),
      binding: MerchantAppLoginBinding(),
    ),
    // 主页
    GetPage(
      name: merchantMain,
      page: () => const MerchantMainPage(),
      binding: MerchantMainBinding(),
    ),
    // 商户首页
    GetPage(name: merchantHome, page: () => MerchantHomePage()),
    GetPage(name: merchantProfile, page: () => MerchantProfilePage()),
    // 设置页
    GetPage(
      name: merchantSetting,
      page: () => MerchantSettingPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => MerchantSettingController());
      }),
    ),
    // 修改密码页
    GetPage(
      name: merchantChangePassword,
      page: () => const MerchantChangePasswordPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => MerchantChangePasswordController());
      }),
    ),
    // 收款
    GetPage(
      name: merchantRecieveCash,
      page: () => const MerchantReceiveCashPage(),
      binding: MerchantReceiveCashBinding(),
    ),
    // 扫码
    GetPage(
      name: merchantScan,
      page: () => const MerchantScanQRCodePage(),
      binding: MerchantScanQRCodeBinding(),
    ),
    // 生成二维码
    GetPage(
      name: merchantQRCode,
      page: () => const MerchantGenerateCodePage(),
      binding: MerchantGenerateCodeBinding(),
    ),
    // 404页面
    GetPage(name: notFound, page: () => NotFoundPage()),
    // 商户首页
    GetPage(
      name: merchantWebview,
      page: () => const MerchantWebViewPage(),
      binding: MerchantWebViewBinding(),
    ),
    //邀请
    GetPage(
      name: merchantInvite,
      page: () => const MerchantInvitePage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => MerchantInviteController());
      }),
    ),
    //API日志
    GetPage(
      name: merchantApiLog,
      page: () => const MerchantApiOperatorPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => MerchantApiLogController());
      }),
    ),
    //订单退款
    GetPage(
      name: merchantOrderRefund,
      page: () => const MerchantOrderRefundPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => MerchantOrderRefundController());
      }),
    ),
    //订单详情
    GetPage(
      name: merchantorderdetail,
      page: () => MerchantOrderDetailPage(),
      binding: MerchantOrderDetailBinding(),
    ),
    //账号管理
    GetPage(
      name: accountManagement,
      page: () => const MerchantaccountmanagerPage(),
      binding: MerchantaccountmanagerBinding(),
    ),
    //添加账号
    GetPage(
      name: merchantAccountManagerAdd,
      page: () => const MerchantAccountAddPage(),
      binding: MerchantAccountAddBinding(),
    ),
    //选择手机归属地
    GetPage(
      name: choosePhoneCountry,
      page: () => const ChoosePhoneCountryPage(),
      binding: ChoosePhoneCountryBinding(),
    ),
    //门店管理
    GetPage(
      name: merchantShopManager,
      page: () => const MerchantshopmanagerPage(),
      binding: MerchantshopmanagerBinding(),
    ),
    //门店添加
    GetPage(
      name: merchantShopAdd,
      page: () => const MerchantShopAddPage(),
      binding: MerchantShopAddBinding(),
    ),
    // 钱包页面
    GetPage(
      name: merchantWallet,
      page: () => const MerchantWalletPage(),
      binding: MerchantWalletBinding(),
    ),
    // 选择位置
    GetPage(
      name: chooseLocation,
      page: () => const ChooseLocationPage(),
      binding: ChooseLocationBinding(),
    ),
    // 选择位置搜索
    GetPage(
      name: chooseLocationSearch,
      page: () => const ChooseLocationSearchPage(),
      binding: ChooseLocationSearchBinding(),
    ),
    // 转账
    GetPage(
      name: merchantWalletIdCheck,
      page: () => const MerchantTransferIdCheckPage(),
      binding: MerchantTransferIdBinding(),
    ),
    // 转账确认
    GetPage(
      name: merchantTransferConfirm,
      page: () => const MerchantTransferConfirmPage(),
      binding: MerchantTransferConfirmBinding(),
    ),
    // 结算管理
    GetPage(
      name: merchantSettleManager,
      page: () => MerchantSettleManagerPage(),
      binding: MerchantSettleManagerBinding(),
    ),
    // //提现
    // GetPage(
    //   name: merchantWithdraw,
    //   page: () => const MerchantWithdrawPage(),
    //   binding: MerchantWithdrawBinding(),
    // ),
    // // 交易记录
    // GetPage(
    //   name: merchantTransactionRecord,
    //   page: () => const MerchantTransactionRecordPage(),
    //   binding: MerchantTransactionRecordBinding(),
    // ),
    // 充值提现
    GetPage(
      name: merchantRechargeWithdrawWebView,
      page: () => const MerchantRechargeWithdrawWebViewPage(),
      binding: MerchantRechargeWithdrawWebViewBinding(),
    ),
    // 订单记录简要信息
    GetPage(
      name: merchantOrderRecordBrief,
      page: () => const MerchantOrderRecordBriefPage(),
      binding: MerchantOrderRecordBriefBinding(),
    ),
    // 订单记录详情
    GetPage(
      name: merchantOrderRecordDetail,
      page: () => const MerchantOrderRecordDetailPage(),
      binding: MerchantOrderRecordDetailBinding(),
    ),
    //短信验证码
    GetPage(
      name: merchantTransferFinalSmscode,
      page: () => const MerchantTransferFinalSmsCodePage(),
      binding: MerchantTransferFinalSmsCodeBinding(),
    ),
    // 支付结果
    GetPage(
      name: merchantPayResult,
      page: () => const MerchantPayResultPage(),
      binding: MerchantPayResultBinding(),
    ),
    // 转账回执
    GetPage(
      name: merchantTransferRecordReceipt,
      page: () => const MerchantTransferRecordReceiptPage(),
      binding: MerchantTransferRecordReceiptBinding(),
    ),
    // 结算详情
    GetPage(
      name: merchantSettleDetail,
      page: () => const MerchantSettleDetailPage(),
      binding: MerchantSettleDetailBinding(),
    ),
    GetPage(
      name: storeSelect,
      page: () => const StoreSelectPage(),
      binding: StoreSelectBinding(),
    ),
    GetPage(
      name: proxyPageSetting,
      page: () => ProxyPageSettingPage(),
      binding: ProxyPageSettingBinding(),
    ),
    // 环境切换
    GetPage(
      name: merchantEnvironmentChange,
      page: () => const MerchantEnvironmentChangePage(),
      binding: MerchantEnvironmentChangeBinding(),
    ),
    GetPage(
      name: merchantWithdrawBank,
      page: () => const MerchantWithdrawBankPage(),
      binding: MerchantWithdrawBankBinding(),
    ),
    GetPage(
      name: merchantBankCardManagement,
      page: () => const MerchantBankCardPage(),
      binding: MerchantBankCardBinding(),
    ),
    GetPage(
      name: merchantBankList,
      page: () => MerchantBankListPage(),
      binding: MerchantBankListBinding(),
    ),
    GetPage(
      name: merchantBindBankCard,
      page: () => const MerchantBindBankCardPage(),
      binding: MerchantBindBankCardBinding(),
    ),
    GetPage(
      name: merchantBankCardDetail,
      page: () => MerchantBankCardDetailPage(),
      binding: MerchantBankCardDetailBinding(),
    ),
  ];

  /// 老挝APP页面
  static final List<GetPage> laowoPages = [
    GetPage(name: laowoappcontainer, page: () => const NewPayHomePage()),
  ];

  /// 获取所有页面配置
  static List<GetPage> getPages(AppType type) {
    switch (type) {
      case AppType.newPay:
        return newPayPages;
      case AppType.merchant:
        return merchantPages;
      case AppType.laowo:
        return laowoPages;
      case AppType.wenlai:
        return [];
      default:
        return [];
    }
  }

  /// 获取初始路由
  static String getInitialRoute(AppType type) {
    switch (type) {
      case AppType.newPay:
        return container;
      case AppType.merchant:
        return merchantLogin;
      case AppType.laowo:
        return laowoappcontainer;
      case AppType.wenlai:
        return '';
      default:
        return '';
    }
  }
}
