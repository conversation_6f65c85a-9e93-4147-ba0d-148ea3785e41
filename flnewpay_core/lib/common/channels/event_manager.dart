import 'dart:async';
import 'package:flutter/foundation.dart'; // Import for kIsWeb
import 'package:flutter/services.dart';

class EventManager {
  // 1. 单例模式
  static final EventManager _instance = EventManager._internal();

  factory EventManager() => _instance;

  EventManager._internal() {
    // 初始化 Platform Channel
    _platformChannel = const MethodChannel('com.flnewpaycore.channel/native');
    _platformChannel.setMethodCallHandler(_handleNativeCall); // Set handler
  }

  // 2. Platform Channel
  late MethodChannel _platformChannel;

  // 3. 事件订阅与回调
  final Map<String, List<Function>> _eventListeners = {};

  // 4. 订阅事件
  void on(String eventName, Function callback) {
    if (_eventListeners.containsKey(eventName)) {
      _eventListeners[eventName]!.add(callback);
    } else {
      _eventListeners[eventName] = [callback];
    }
  }

  // 5. 取消订阅事件
  void off(String eventName, Function callback) {
    if (_eventListeners.containsKey(eventName)) {
      _eventListeners[eventName]!.remove(callback);
    }
  }

  // 6. 发送事件到原生端
  Future<dynamic> emit(String eventName, {Map<String, dynamic>? data}) async {
    try {
      // 构建消息
      final message = {'name': eventName, 'data': data ?? {}};

      // 调用原生方法
      final result = await _platformChannel.invokeMethod(
        'handleFlutterCall',
        message,
      );
      return result;
    } on PlatformException catch (e) {
      debugPrint("Error invoking native method: ${e.message}");
      return null;
    }
  }

  // 7. 处理来自原生端的消息
  Future<dynamic> _handleNativeCall(MethodCall call) async {
    final eventName = call.method;
    final data = call.arguments;

    debugPrint('Received event from native: $eventName, data: $data');

    if (_eventListeners.containsKey(eventName)) {
      for (var callback in _eventListeners[eventName]!) {
        callback(data); // Pass data to the callback
      }
      return 'Event handled'; // Indicate success
    } else {
      debugPrint('No listener for event: $eventName');
      return 'No handler found';
    }
  }
}
