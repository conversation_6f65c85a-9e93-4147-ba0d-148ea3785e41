
import pandas as pd

# 读取Excel文件
df = pd.read_excel('flnewpay_core/lib/common/translations/merchant App translation.xlsx')

# 生成Dart代码
dart_content = '''import 'package:get/get.dart';

class NewMessages extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
'''

# 生成英文部分
dart_content += '    \\'en_US\\': {\\n' # type: ignore
for index, row in df.iterrows():
    key = str(row['Key']).strip()
    en_value = str(row['en_US']).strip() if pd.notna(row['en_US']) else ''
    if key and en_value and key != 'nan' and en_value != 'nan':
        # 转义单引号和反斜杠
        en_value = en_value.replace('\\\\', '\\\\\\\\').replace('\\'', '\\\\\\'')
        dart_content += f'      \\'{key}\\': \\'{en_value}\\',\\n'

dart_content += '    },\\n'

# 生成中文部分
dart_content += '    \\'zh_CN\\': {\\n'
for index, row in df.iterrows():
    key = str(row['Key']).strip()
    zh_value = str(row['zh_CN']).strip() if pd.notna(row['zh_CN']) else ''
    if key and zh_value and key != 'nan' and zh_value != 'nan':
        # 转义单引号和反斜杠
        zh_value = zh_value.replace('\\\\', '\\\\\\\\').replace('\\'', '\\\\\\'')
        dart_content += f'      \\'{key}\\': \\'{zh_value}\\',\\n'

dart_content += '    },\\n'

# 生成老挝语部分
dart_content += '    \\'lo_LA\\': {\\n'
for index, row in df.iterrows():
    key = str(row['Key']).strip()
    lo_value = str(row['lo_LA']).strip() if pd.notna(row['lo_LA']) else ''
    if key and lo_value and key != 'nan' and lo_value != 'nan':
        # 转义单引号和反斜杠
        lo_value = lo_value.replace('\\\\', '\\\\\\\\').replace('\\'', '\\\\\\'')
        dart_content += f'      \\'{key}\\': \\'{lo_value}\\',\\n'

dart_content += '    },\\n'
dart_content += '  };\\n}'

# 写入文件
with open('flnewpay_core/lib/common/translations/newmessages.dart', 'w', encoding='utf-8') as f:
    f.write(dart_content)

print('newmessages.dart 文件已生成完成！')

print(f'总共处理了 {len(df)} 行数据')

